
export const IconTypes = [ 
  'doc_hide_components/wrench_filled',
  'doc_hide_components/wrench_outlined',
  'doc_hide_components/save_outlined',
  'doc_hide_components/save_filled',
  'doc_hide_components/brain_outlined',
  'doc_hide_components/brain_filled',
  'doc_hide_components/builder_outlined',
  'doc_hide_components/builder_filled',
  'doc_hide_components/node_agent_outlined',
  'doc_hide_components/node_database_outlined',
  'doc_hide_components/node_aipage_outlined',
  'doc_hide_components/node_dashboard_outlined',
  'doc_hide_components/node_automation_outlined',
  'doc_hide_components/user_experts_filled',
  'doc_hide_components/user_experts_outlined',
  'doc_hide_components/send_outlined',
  'doc_hide_components/send_filled',
  'doc_hide_components/mermaid_outlined',
  'doc_hide_components/mermaid_filled',
  'doc_hide_components/folder_personal_outlined',
  'doc_hide_components/folder_personal_filled',
  'doc_hide_components/folder_space_outlined',
  'doc_hide_components/folder_space_filled',
  'doc_hide_components/smart_filled',
  'doc_hide_components/translation_ai_filled',
  'doc_hide_components/write_ai_filled',
  'doc_hide_components/write_ai_outlined',
  'doc_hide_components/translation_ai_outlined',
  'doc_hide_components/generate_ai_filled',
  'doc_hide_components/generate_ai_outlined',
  'doc_hide_components/user_guest_filled',
  'doc_hide_components/user_guest_outlined',
  'doc_hide_components/verification_code_outlined',
  'doc_hide_components/verification_code_filled',
  'doc_hide_components/array_outlined',
  'doc_hide_components/array_filled',
  'doc_hide_components/comingsoon_filled',
  'doc_hide_components/comingsoon_outlined',
  'doc_hide_components/voice_ai_outlined',
  'doc_hide_components/voice_ai_filled',
  'doc_hide_components/image_ai_filled',
  'doc_hide_components/video_ai_filled',
  'doc_hide_components/text_ai_filled',
  'doc_hide_components/daterange_filled',
  'doc_hide_components/daterange_outlined',
  'doc_hide_components/text_ai_outlined',
  'doc_hide_components/video_ai_outlined',
  'doc_hide_components/image_ai_outlined',
  'doc_hide_components/cut_outlined',
  'doc_hide_components/json_filled',
  'doc_hide_components/json_outlined',
  'doc_hide_components/cut_filled',
  'doc_hide_components/logic_outlined',
  'doc_hide_components/logic_filled',
  'doc_hide_components/user_guests_filled',
  'doc_hide_components/user_guests_outlined',
  'doc_hide_components/line_filled',
  'doc_hide_components/line_outlined',
  'doc_hide_components/certification_filled',
  'doc_hide_components/certification_outlined',
  'doc_hide_components/upgrade_circle_color_filled',
  'doc_hide_components/play2_filled',
  'doc_hide_components/play2_outlined',
  'doc_hide_components/pause2_outlined',
  'doc_hide_components/pause2_filled',
  'doc_hide_components/discord_filled',
  'doc_hide_components/coin_light_filled',
  'doc_hide_components/coin_dark_filled',
  'doc_hide_components/speech_filled',
  'doc_hide_components/speech_outlined',
  'components/smart_outlined',
  'doc_hide_components/logo_text_filled',
  'doc_hide_components/invite_small_filled',
  'doc_hide_components/email_background_filled',
  'doc_hide_components/pic_logo_filled',
  'doc_hide_components/comment_bj_small_filled',
  'doc_hide_components/logo_filled',
  'doc_hide_components/solution_outlined',
  'doc_hide_components/upload_outlined',
  'doc_hide_components/previous_filled',
  'doc_hide_components/logo_text_en_filled',
  'doc_hide_components/logo_large_filled',
  'doc_hide_components/next_filled',
  'doc_hide_components/logo_white_filled',
  'doc_hide_components/apilogotext__filled',
  'doc_hide_components/comment_bj_entire_filled',
  'doc_hide_components/head_background_filled',
  'doc_hide_components/solution_small_outlined',
  'doc_hide_components/bug_outlined',
  'doc_hide_components/apilogo__filled',
  'doc_hide_components/logotext_filled',
  'doc_hide_components/logo_purple_filled',
  'doc_hide_components/fitview_outlined',
  'doc_hide_components/vikaby_filled',
  'doc_hide_components/vikaby_outlined',
  'doc_hide_components/comment_bg_filled',
  'doc_hide_components/select_mark_filled',
  'doc_hide_components/linkedIn_filled',
  'doc_hide_components/apple_filled',
  'doc_hide_components/google_filled',
  'doc_hide_components/twitter_filled',
  'doc_hide_components/github_filled',
  'doc_hide_components/twitter_outlined',
  'doc_hide_components/linkedin_outlined',
  'doc_hide_components/dingding_filled',
  'doc_hide_components/feishu_color_filled',
  'doc_hide_components/qq_filled',
  'doc_hide_components/wechat_color_filled',
  'doc_hide_components/alipay_color_filled',
  'doc_hide_components/wecom_color_filled',
  'doc_hide_components/alipay_filled',
  'doc_hide_components/dingding_color_filled',
  'doc_hide_components/feishu_filled',
  'doc_hide_components/qq_color_filled',
  'doc_hide_components/wechatpay_color_filled',
  'doc_hide_components/wechatpay_filled',
  'doc_hide_components/wechat_filled',
  'doc_hide_components/wecom_filled',
  'doc_hide_components/emailfeedback_outlined',
  'doc_hide_components/subtract_circle_color_filled',
  'doc_hide_components/info_circle_color_filled',
  'doc_hide_components/warn_circle_color_filled',
  'doc_hide_components/danger_circle_color_filled',
  'doc_hide_components/check_circle_color_filled',
  'doc_hide_components/liveChat_filled',
  'doc_hide_components/silver_dark_filled',
  'doc_hide_components/enterprise_dark_filled',
  'doc_hide_components/bronze_dark_filled',
  'doc_hide_components/gold_dark_filled',
  'doc_hide_components/enterprise_light_filled',
  'doc_hide_components/gold_light_filled',
  'doc_hide_components/silver_light_filled',
  'doc_hide_components/bronze_light_filled',
  'doc_hide_components/chart_line_stack_filled',
  'doc_hide_components/chart_line_normal_filled',
  'doc_hide_components/chart_line_percent_filled',
  'doc_hide_components/chart_column_normal_filled',
  'doc_hide_components/space_info_filled',
  'doc_hide_components/webhook_filled',
  'doc_hide_components/invite_box_filled',
  'doc_hide_components/bronze_filled',
  'doc_hide_components/enterprise_filled',
  'doc_hide_components/gold_filled',
  'doc_hide_components/silver_filled',
  'doc_hide_components/chart_column_percent_filled',
  'doc_hide_components/chart_pie_filled',
  'doc_hide_components/chart_bar_stack_filled',
  'doc_hide_components/chart_bar_normal_filled',
  'doc_hide_components/chart_scatter_plotnormal_filled',
  'doc_hide_components/chart_bar_percent_filled',
  'doc_hide_components/chart_column_stack_filled',
  'doc_hide_components/chart_dount_filled',
  'doc_hide_components/embed_filled',
  'doc_hide_components/embed_outlined',
  'doc_hide_components/cursor_button_filled',
  'doc_hide_components/cursor_button_outlined',
  'doc_hide_components/placeholder_filled',
  'doc_hide_components/placeholder_outlined',
  'doc_hide_components/one_way_link_filled',
  'doc_hide_components/one_way_link_outlined',
  'doc_hide_components/two_way_link_filled',
  'doc_hide_components/two_way_link_outlined',
  'doc_hide_components/Archive_outlined',
  'doc_hide_components/Archive_filled',
  'doc_hide_components/dislike_outlined',
  'doc_hide_components/dislike_filled',
  'doc_hide_components/toggle_outlined',
  'doc_hide_components/toggle_filled',
  'doc_hide_components/refresh_filled',
  'doc_hide_components/email_outlined',
  'doc_hide_components/user_role_filled',
  'doc_hide_components/user_role_outlined',
  'doc_hide_components/train_filled',
  'doc_hide_components/train_outlined',
  'doc_hide_components/up_and_down_outlined',
  'doc_hide_components/up_and_down_filled',
  'doc_hide_components/enter_filled',
  'doc_hide_components/enter_outlined',
  'doc_hide_components/time_outlined',
  'doc_hide_components/unchecked_outlined',
  'doc_hide_components/unchecked_filled',
  'doc_hide_components/warn_circle_outlined',
  'doc_hide_components/warn_circle_filled',
  'doc_hide_components/organizational_outlined',
  'doc_hide_components/organizational_filled',
  'doc_hide_components/notification_check_outlined',
  'doc_hide_components/notification_check_filled',
  'doc_hide_components/grid_mirror_outlined',
  'doc_hide_components/architecture_mirror_outlined',
  'doc_hide_components/kanban_filled',
  'doc_hide_components/architecture_filled',
  'doc_hide_components/gantt_mirror_outlined',
  'doc_hide_components/kanban_mirror_outlined',
  'doc_hide_components/gallery_mirror_outlined',
  'doc_hide_components/gallery_mirror_filled',
  'doc_hide_components/calendar_mirror_outlined',
  'doc_hide_components/calendar_mirror_filled',
  'doc_hide_components/architecture_mirror_filled',
  'doc_hide_components/gantt_mirror_filled',
  'doc_hide_components/kanban_mirror_filled',
  'doc_hide_components/grid_mirror_filled',
  'doc_hide_components/gantt_filled',
  'doc_hide_components/architecture_outlined',
  'doc_hide_components/gantt_outlined',
  'doc_hide_components/kanban_outlined',
  'doc_hide_components/italics_filled',
  'doc_hide_components/body_outlined',
  'doc_hide_components/headline_2_outlined',
  'doc_hide_components/headline_1_outlined',
  'doc_hide_components/ordered_outlined',
  'doc_hide_components/italics_outlined',
  'doc_hide_components/unordered_outlined',
  'doc_hide_components/org_zoom_out_outlined',
  'doc_hide_components/task_list_outlined',
  'doc_hide_components/code_outlined',
  'doc_hide_components/headline_3_outlined',
  'doc_hide_components/text_right_outlined',
  'doc_hide_components/underline_outlined',
  'doc_hide_components/strikethrough_outlined',
  'doc_hide_components/bold_outlined',
  'doc_hide_components/text_middle_outlined',
  'doc_hide_components/text_left_outlined',
  'doc_hide_components/quote_outlined',
  'doc_hide_components/dividing_line_outlined',
  'doc_hide_components/highlight_outlined',
  'doc_hide_components/bold_filled',
  'doc_hide_components/text_left_filled',
  'doc_hide_components/body_filled',
  'doc_hide_components/code_filled',
  'doc_hide_components/org_zoom_out_filled',
  'doc_hide_components/quote_filled',
  'doc_hide_components/dividing_line_filled',
  'doc_hide_components/underline_filled',
  'doc_hide_components/text_middle_filled',
  'doc_hide_components/headline_1_filled',
  'doc_hide_components/strikethrough_filled',
  'doc_hide_components/text_right_filled',
  'doc_hide_components/task_list_filled',
  'doc_hide_components/highlight_filled',
  'doc_hide_components/unordered_filled',
  'doc_hide_components/headline_3_filled',
  'doc_hide_components/ordered_filled',
  'doc_hide_components/headline_2_filled',
  'components/automation_outlined',
  'components/automation_filled',
  'components/refresh_outlined',
  'components/paste_filled',
  'components/esc_outlined',
  'components/esc_filled',
  'components/log_outlined',
  'components/log_filled',
  'components/style_outlined',
  'components/style_filled',
  'components/loading_filled',
  'components/loading_outlined',
  'components/shield_security_filled',
  'components/shield_security_outlined',
  'components/compass_outlined',
  'components/compass_filled',
  'components/setting_2_outlined',
  'components/setting_2_filled',
  'components/currency_USD_filled',
  'components/adjustment_outlined',
  'components/question_circle_filled',
  'components/question_circle_outlined',
  'components/info_circle_outlined',
  'components/description_outlined',
  'components/description_filled',
  'components/form_add_filled',
  'components/info_filled',
  'components/user_admin_outlined',
  'components/user_admin_filled',
  'components/star_2_filled',
  'components/star_2_outlined',
  'components/bank_outlined',
  'components/conical_right_outlined',
  'components/conical_left_outlined',
  'components/conical_down_outlined',
  'components/conical_right_filled',
  'components/conical_left_filled',
  'components/conical_down_filled',
  'components/conical_up_filled',
  'components/conical_up_outlined',
  'components/transfer_filled',
  'components/transfer_outlined',
  'components/question_outlined',
  'components/warn_filled',
  'components/question_filled',
  'components/warn_outlined',
  'components/info_outlined',
  'components/sidescreen_filled',
  'components/middlescreen_filled',
  'components/middlescreen_outlined',
  'components/sidescreen_outlined',
  'components/bank_filled',
  'components/bulb_filled',
  'components/bulb_outlined',
  'components/rocket_filled',
  'components/rocket_outlined',
  'components/network_normal_filled',
  'components/network_connect_filled',
  'components/network_error_filled',
  'components/network_error_outlined',
  'components/network_normal_outlined',
  'components/network_connect_outlined',
  'components/stopwatch_filled',
  'components/keyboard_outlined',
  'components/keyboard_filled',
  'components/book_filled',
  'components/book_outlined',
  'components/workbench_filled',
  'components/stopwatch_outlined',
  'components/workbench_outlined',
  'components/attention_outlined',
  'components/unpublish_filled',
  'components/publish_filled',
  'components/unpublish_outlined',
  'components/publish_outlined',
  'components/file_search_outlined',
  'components/file_search_filled',
  'components/collapse_3_filled',
  'components/collapse_3_open_filled',
  'components/attention_filled',
  'components/like_outlined',
  'components/like_filled',
  'components/service_outlined',
  'components/clear_filled',
  'components/clear_outlined',
  'components/service_filled',
  'components/link_disconnect_filled',
  'components/link_disconnect_outlined',
  'components/download_filled',
  'components/download_outlined',
  'components/rotate_outlined',
  'components/rotate_filled',
  'components/calendar_filled',
  'components/grid_filled',
  'components/gallery_filled',
  'components/gallery_outlined',
  'components/calendar_outlined',
  'components/grid_outlined',
  'components/form_outlined',
  'components/form_filled',
  'components/form_add_outlined',
  'components/chevron_left_outlined',
  'components/image_outlined',
  'components/delete_filled',
  'components/delete_outlined',
  'components/file_excel_filled',
  'components/file_filled',
  'components/folder_right_filled',
  'components/folder_open_filled',
  'components/folder_open_outlined',
  'components/folder_empty_filled',
  'components/folder_empty_outlined',
  'components/folder_normal_filled',
  'components/folder_normal_outlined',
  'components/file_outlined',
  'components/datasheet_filled',
  'components/file_add_filled',
  'components/file_csv_filled',
  'components/folder_add_filled',
  'components/folder_down_filled',
  'components/folder_up_filled',
  'components/folder_left_filled',
  'components/folder_down_outlined',
  'components/folder_up_outlined',
  'components/folder_left_outlined',
  'components/datasheet_outlined',
  'components/folder_right_outlined',
  'components/file_csv_outlined',
  'components/file_excel_outlined',
  'components/file_add_outlined',
  'components/folder_add_outlined',
  'components/user_add_filled',
  'components/user_edit_filled',
  'components/user_filled',
  'components/user_group_filled',
  'components/user_edit_outlined',
  'components/user_outlined',
  'components/user_add_outlined',
  'components/user_group_outlined',
  'components/list_filled',
  'components/cascade_filled',
  'components/close_circle_outlined',
  'components/check_circle_outlined',
  'components/close_circle_filled',
  'components/info_circle_filled',
  'components/check_circle_filled',
  'components/star_cross_outlined',
  'components/autonumber_filled',
  'components/satellite_filled',
  'components/emoji_filled',
  'components/home_outlined',
  'components/telephone_outlined',
  'components/telephone_filled',
  'components/notification_filled',
  'components/rowhight_medium_filled',
  'components/qrcode_filled',
  'components/gift_filled',
  'components/shield_check_filled',
  'components/mortarboard_filled',
  'components/linktable_filled',
  'components/robot_filled',
  'components/time_filled',
  'components/mobilephone_filled',
  'components/longtext_filled',
  'components/select_single_filled',
  'components/alarm_filled',
  'components/rowhight_medium_outlined',
  'components/rowhight_short_outlined',
  'components/longtext_outlined',
  'components/mobilephone_outlined',
  'components/satellite_outlined',
  'components/horn_outlined',
  'components/more_stand_outlined',
  'components/link_outlined',
  'components/pin_outlined',
  'components/add_circle_outlined',
  'components/community_filled',
  'components/community_outlined',
  'components/manage_application_filled',
  'components/manage_application_outlined',
  'components/department_outlined',
  'components/department_filled',
  'components/home_filled',
  'components/planet_outlined',
  'components/planet_filled',
  'components/star_cross_filled',
  'components/advise_outlined',
  'components/list_outlined',
  'components/advise_filled',
  'components/widget_filled',
  'components/autonumber_outlined',
  'components/history_outlined',
  'components/video_filled',
  'components/video_outlined',
  'components/currency_USD_outlined',
  'components/roadmap_filled',
  'components/roadmap_outlined',
  'components/history_filled',
  'components/checkbox_filled',
  'components/number_filled',
  'components/attachment_filled',
  'components/rowhight_extremhigh_filled',
  'components/rowhight_high_filled',
  'components/link_filled',
  'components/shield_add_outlined',
  'components/horn_filled',
  'components/class_filled',
  'components/desktop_filled',
  'components/shield_add_filled',
  'components/more_filled',
  'components/more_stand_filled',
  'components/pin_filled',
  'components/email_filled',
  'components/mirror_filled',
  'components/api_filled',
  'components/rowhight_short_filled',
  'components/web_filled',
  'components/formula_filled',
  'components/percent_filled',
  'components/currency_CNY_filled',
  'components/select_multiple_filled',
  'components/text_filled',
  'components/image_filled',
  'components/web_outlined',
  'components/emoji_outlined',
  'components/mirror_outlined',
  'components/robot_outlined',
  'components/api_outlined',
  'components/widget_outlined',
  'components/rowhight_extremhigh_outlined',
  'components/rowhight_high_outlined',
  'components/cascade_outlined',
  'components/linktable_outlined',
  'components/percent_outlined',
  'components/currency_CNY_outlined',
  'components/formula_outlined',
  'components/number_outlined',
  'components/attachment_outlined',
  'components/select_multiple_outlined',
  'components/select_single_outlined',
  'components/checkbox_outlined',
  'components/text_outlined',
  'components/dashboard_filled',
  'components/class_outlined',
  'components/desktop_outlined',
  'components/mortarboard_outlined',
  'components/alarm_outlined',
  'components/star_outlined',
  'components/star_filled',
  'components/qrcode_outlined',
  'components/notification_outlined',
  'components/gift_outlined',
  'components/shield_check_outlined',
  'components/more_outlined',
  'components/dashboard_outlined',
  'components/import_outlined',
  'components/group_outlined',
  'components/sync_on_filled',
  'components/rank_up_filled',
  'components/sync_on_outlined',
  'components/eye_close_filled',
  'components/eye_open_filled',
  'components/collapse_2_outlined',
  'components/collapse_2_open_outlined',
  'components/collapse_outlined',
  'components/collapse_open_outlined',
  'components/comment_filled',
  'components/collapse_open_filled',
  'components/close_outlined',
  'components/autosave_outlined',
  'components/sync_off_outlined',
  'components/test_outlined',
  'components/sync_off_filled',
  'components/pause_filled',
  'components/play_filled',
  'components/play_outlined',
  'components/pause_outlined',
  'components/goto_outlined',
  'components/goto_filled',
  'components/logout_outlined',
  'components/import_filled',
  'components/logout_filled',
  'components/connect_outlined',
  'components/connect_filled',
  'components/setting_filled',
  'components/test_filled',
  'components/group_filled',
  'components/freeze_filled',
  'components/autosave_filled',
  'components/adjustment_filled',
  'components/position_filled',
  'components/close_filled',
  'components/collapse_filled',
  'components/collapse_2_open_filled',
  'components/collapse_2_filled',
  'components/edit_filled',
  'components/expand_filled',
  'components/narrow_filled',
  'components/copy_filled',
  'components/duplicate_filled',
  'components/undo_filled',
  'components/redo_filled',
  'components/disabled_filled',
  'components/add_filled',
  'components/rank_down_filled',
  'components/rank_filled',
  'components/newtab_filled',
  'components/drag_filled',
  'components/restore_outlined',
  'components/reload_filled',
  'components/restore_filled',
  'components/look_up_filled',
  'components/search_filled',
  'components/check_filled',
  'components/filter_filled',
  'components/freeze_outlined',
  'components/position_outlined',
  'components/comment_outlined',
  'components/disabled_outlined',
  'components/setting_outlined',
  'components/rank_outlined',
  'components/filter_outlined',
  'components/redo_outlined',
  'components/undo_outlined',
  'components/lookup_outlined',
  'components/eye_open_outlined',
  'components/eye_close_outlined',
  'components/collapse_3_open_outlined',
  'components/collapse_3_outlined',
  'components/rank_down_outlined',
  'components/share_filled',
  'components/rank_up_outlined',
  'components/share_outlined',
  'components/paste_outlined',
  'components/subtract_circle_filled',
  'components/subtract_circle_outlined',
  'components/lock_outlined',
  'components/drag_outlined',
  'components/lock_filled',
  'components/newtab_outlined',
  'components/search_outlined',
  'components/reload_outlined',
  'components/add_circle_filled',
  'components/check_outlined',
  'components/duplicate_outlined',
  'components/edit_outlined',
  'components/narrow_outlined',
  'components/expand_outlined',
  'components/copy_outlined',
  'components/add_outlined',
  'components/triangle_right_outlined',
  'components/triangle_up_outlined',
  'components/triangle_down_outlined',
  'components/triangle_left_outlined',
  'components/arrow_up_filled',
  'components/arrow_right_filled',
  'components/arrow_down_filled',
  'components/arrow_left_filled',
  'components/chevron_double_up_filled',
  'components/chevron_double_down_filled',
  'components/chevron_double_right_filled',
  'components/chevron_double_left_filled',
  'components/chevron_up_filled',
  'components/chevron_right_filled',
  'components/chevron_down_filled',
  'components/chevron_left_filled',
  'components/chevron_double_up_outlined',
  'components/chevron_double_down_outlined',
  'components/chevron_double_right_outlined',
  'components/chevron_double_left_outlined',
  'components/triangle_down_filled',
  'components/triangle_right_filled',
  'components/triangle_up_filled',
  'components/triangle_left_filled',
  'components/chevron_up_outlined',
  'components/chevron_right_outlined',
  'components/chevron_down_outlined',
  'components/arrow_left_outlined',
  'components/arrow_right_outlined',
  'components/arrow_up_outlined',
  'components/arrow_down_outlined',

] as const;

export type IconType = typeof IconTypes[number];
            