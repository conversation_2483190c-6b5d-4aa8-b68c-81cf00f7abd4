
import dynamic from 'next/dynamic';
import type { IconType } from '@bika/types/ui/bo';

export function loadIcon(iconType: IconType) {
  switch (iconType) {

    case 'doc_hide_components/wrench_filled':
      return dynamic(() => import('./doc_hide_components/wrench_filled').then((mod) => mod.default));

    case 'doc_hide_components/wrench_outlined':
      return dynamic(() => import('./doc_hide_components/wrench_outlined').then((mod) => mod.default));

    case 'doc_hide_components/save_outlined':
      return dynamic(() => import('./doc_hide_components/save_outlined').then((mod) => mod.default));

    case 'doc_hide_components/save_filled':
      return dynamic(() => import('./doc_hide_components/save_filled').then((mod) => mod.default));

    case 'doc_hide_components/brain_outlined':
      return dynamic(() => import('./doc_hide_components/brain_outlined').then((mod) => mod.default));

    case 'doc_hide_components/brain_filled':
      return dynamic(() => import('./doc_hide_components/brain_filled').then((mod) => mod.default));

    case 'doc_hide_components/builder_outlined':
      return dynamic(() => import('./doc_hide_components/builder_outlined').then((mod) => mod.default));

    case 'doc_hide_components/builder_filled':
      return dynamic(() => import('./doc_hide_components/builder_filled').then((mod) => mod.default));

    case 'doc_hide_components/node_agent_outlined':
      return dynamic(() => import('./doc_hide_components/node_agent_outlined').then((mod) => mod.default));

    case 'doc_hide_components/node_database_outlined':
      return dynamic(() => import('./doc_hide_components/node_database_outlined').then((mod) => mod.default));

    case 'doc_hide_components/node_aipage_outlined':
      return dynamic(() => import('./doc_hide_components/node_aipage_outlined').then((mod) => mod.default));

    case 'doc_hide_components/node_dashboard_outlined':
      return dynamic(() => import('./doc_hide_components/node_dashboard_outlined').then((mod) => mod.default));

    case 'doc_hide_components/node_automation_outlined':
      return dynamic(() => import('./doc_hide_components/node_automation_outlined').then((mod) => mod.default));

    case 'doc_hide_components/user_experts_filled':
      return dynamic(() => import('./doc_hide_components/user_experts_filled').then((mod) => mod.default));

    case 'doc_hide_components/user_experts_outlined':
      return dynamic(() => import('./doc_hide_components/user_experts_outlined').then((mod) => mod.default));

    case 'doc_hide_components/send_outlined':
      return dynamic(() => import('./doc_hide_components/send_outlined').then((mod) => mod.default));

    case 'doc_hide_components/send_filled':
      return dynamic(() => import('./doc_hide_components/send_filled').then((mod) => mod.default));

    case 'doc_hide_components/mermaid_outlined':
      return dynamic(() => import('./doc_hide_components/mermaid_outlined').then((mod) => mod.default));

    case 'doc_hide_components/mermaid_filled':
      return dynamic(() => import('./doc_hide_components/mermaid_filled').then((mod) => mod.default));

    case 'doc_hide_components/folder_personal_outlined':
      return dynamic(() => import('./doc_hide_components/folder_personal_outlined').then((mod) => mod.default));

    case 'doc_hide_components/folder_personal_filled':
      return dynamic(() => import('./doc_hide_components/folder_personal_filled').then((mod) => mod.default));

    case 'doc_hide_components/folder_space_outlined':
      return dynamic(() => import('./doc_hide_components/folder_space_outlined').then((mod) => mod.default));

    case 'doc_hide_components/folder_space_filled':
      return dynamic(() => import('./doc_hide_components/folder_space_filled').then((mod) => mod.default));

    case 'doc_hide_components/smart_filled':
      return dynamic(() => import('./doc_hide_components/smart_filled').then((mod) => mod.default));

    case 'doc_hide_components/translation_ai_filled':
      return dynamic(() => import('./doc_hide_components/translation_ai_filled').then((mod) => mod.default));

    case 'doc_hide_components/write_ai_filled':
      return dynamic(() => import('./doc_hide_components/write_ai_filled').then((mod) => mod.default));

    case 'doc_hide_components/write_ai_outlined':
      return dynamic(() => import('./doc_hide_components/write_ai_outlined').then((mod) => mod.default));

    case 'doc_hide_components/translation_ai_outlined':
      return dynamic(() => import('./doc_hide_components/translation_ai_outlined').then((mod) => mod.default));

    case 'doc_hide_components/generate_ai_filled':
      return dynamic(() => import('./doc_hide_components/generate_ai_filled').then((mod) => mod.default));

    case 'doc_hide_components/generate_ai_outlined':
      return dynamic(() => import('./doc_hide_components/generate_ai_outlined').then((mod) => mod.default));

    case 'doc_hide_components/user_guest_filled':
      return dynamic(() => import('./doc_hide_components/user_guest_filled').then((mod) => mod.default));

    case 'doc_hide_components/user_guest_outlined':
      return dynamic(() => import('./doc_hide_components/user_guest_outlined').then((mod) => mod.default));

    case 'doc_hide_components/verification_code_outlined':
      return dynamic(() => import('./doc_hide_components/verification_code_outlined').then((mod) => mod.default));

    case 'doc_hide_components/verification_code_filled':
      return dynamic(() => import('./doc_hide_components/verification_code_filled').then((mod) => mod.default));

    case 'doc_hide_components/array_outlined':
      return dynamic(() => import('./doc_hide_components/array_outlined').then((mod) => mod.default));

    case 'doc_hide_components/array_filled':
      return dynamic(() => import('./doc_hide_components/array_filled').then((mod) => mod.default));

    case 'doc_hide_components/comingsoon_filled':
      return dynamic(() => import('./doc_hide_components/comingsoon_filled').then((mod) => mod.default));

    case 'doc_hide_components/comingsoon_outlined':
      return dynamic(() => import('./doc_hide_components/comingsoon_outlined').then((mod) => mod.default));

    case 'doc_hide_components/voice_ai_outlined':
      return dynamic(() => import('./doc_hide_components/voice_ai_outlined').then((mod) => mod.default));

    case 'doc_hide_components/voice_ai_filled':
      return dynamic(() => import('./doc_hide_components/voice_ai_filled').then((mod) => mod.default));

    case 'doc_hide_components/image_ai_filled':
      return dynamic(() => import('./doc_hide_components/image_ai_filled').then((mod) => mod.default));

    case 'doc_hide_components/video_ai_filled':
      return dynamic(() => import('./doc_hide_components/video_ai_filled').then((mod) => mod.default));

    case 'doc_hide_components/text_ai_filled':
      return dynamic(() => import('./doc_hide_components/text_ai_filled').then((mod) => mod.default));

    case 'doc_hide_components/daterange_filled':
      return dynamic(() => import('./doc_hide_components/daterange_filled').then((mod) => mod.default));

    case 'doc_hide_components/daterange_outlined':
      return dynamic(() => import('./doc_hide_components/daterange_outlined').then((mod) => mod.default));

    case 'doc_hide_components/text_ai_outlined':
      return dynamic(() => import('./doc_hide_components/text_ai_outlined').then((mod) => mod.default));

    case 'doc_hide_components/video_ai_outlined':
      return dynamic(() => import('./doc_hide_components/video_ai_outlined').then((mod) => mod.default));

    case 'doc_hide_components/image_ai_outlined':
      return dynamic(() => import('./doc_hide_components/image_ai_outlined').then((mod) => mod.default));

    case 'doc_hide_components/cut_outlined':
      return dynamic(() => import('./doc_hide_components/cut_outlined').then((mod) => mod.default));

    case 'doc_hide_components/json_filled':
      return dynamic(() => import('./doc_hide_components/json_filled').then((mod) => mod.default));

    case 'doc_hide_components/json_outlined':
      return dynamic(() => import('./doc_hide_components/json_outlined').then((mod) => mod.default));

    case 'doc_hide_components/cut_filled':
      return dynamic(() => import('./doc_hide_components/cut_filled').then((mod) => mod.default));

    case 'doc_hide_components/logic_outlined':
      return dynamic(() => import('./doc_hide_components/logic_outlined').then((mod) => mod.default));

    case 'doc_hide_components/logic_filled':
      return dynamic(() => import('./doc_hide_components/logic_filled').then((mod) => mod.default));

    case 'doc_hide_components/user_guests_filled':
      return dynamic(() => import('./doc_hide_components/user_guests_filled').then((mod) => mod.default));

    case 'doc_hide_components/user_guests_outlined':
      return dynamic(() => import('./doc_hide_components/user_guests_outlined').then((mod) => mod.default));

    case 'doc_hide_components/line_filled':
      return dynamic(() => import('./doc_hide_components/line_filled').then((mod) => mod.default));

    case 'doc_hide_components/line_outlined':
      return dynamic(() => import('./doc_hide_components/line_outlined').then((mod) => mod.default));

    case 'doc_hide_components/certification_filled':
      return dynamic(() => import('./doc_hide_components/certification_filled').then((mod) => mod.default));

    case 'doc_hide_components/certification_outlined':
      return dynamic(() => import('./doc_hide_components/certification_outlined').then((mod) => mod.default));

    case 'doc_hide_components/upgrade_circle_color_filled':
      return dynamic(() => import('./doc_hide_components/upgrade_circle_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/play2_filled':
      return dynamic(() => import('./doc_hide_components/play2_filled').then((mod) => mod.default));

    case 'doc_hide_components/play2_outlined':
      return dynamic(() => import('./doc_hide_components/play2_outlined').then((mod) => mod.default));

    case 'doc_hide_components/pause2_outlined':
      return dynamic(() => import('./doc_hide_components/pause2_outlined').then((mod) => mod.default));

    case 'doc_hide_components/pause2_filled':
      return dynamic(() => import('./doc_hide_components/pause2_filled').then((mod) => mod.default));

    case 'doc_hide_components/discord_filled':
      return dynamic(() => import('./doc_hide_components/discord_filled').then((mod) => mod.default));

    case 'doc_hide_components/coin_light_filled':
      return dynamic(() => import('./doc_hide_components/coin_light_filled').then((mod) => mod.default));

    case 'doc_hide_components/coin_dark_filled':
      return dynamic(() => import('./doc_hide_components/coin_dark_filled').then((mod) => mod.default));

    case 'doc_hide_components/speech_filled':
      return dynamic(() => import('./doc_hide_components/speech_filled').then((mod) => mod.default));

    case 'doc_hide_components/speech_outlined':
      return dynamic(() => import('./doc_hide_components/speech_outlined').then((mod) => mod.default));

    case 'components/smart_outlined':
      return dynamic(() => import('./components/smart_outlined').then((mod) => mod.default));

    case 'doc_hide_components/logo_text_filled':
      return dynamic(() => import('./doc_hide_components/logo_text_filled').then((mod) => mod.default));

    case 'doc_hide_components/invite_small_filled':
      return dynamic(() => import('./doc_hide_components/invite_small_filled').then((mod) => mod.default));

    case 'doc_hide_components/email_background_filled':
      return dynamic(() => import('./doc_hide_components/email_background_filled').then((mod) => mod.default));

    case 'doc_hide_components/pic_logo_filled':
      return dynamic(() => import('./doc_hide_components/pic_logo_filled').then((mod) => mod.default));

    case 'doc_hide_components/comment_bj_small_filled':
      return dynamic(() => import('./doc_hide_components/comment_bj_small_filled').then((mod) => mod.default));

    case 'doc_hide_components/logo_filled':
      return dynamic(() => import('./doc_hide_components/logo_filled').then((mod) => mod.default));

    case 'doc_hide_components/solution_outlined':
      return dynamic(() => import('./doc_hide_components/solution_outlined').then((mod) => mod.default));

    case 'doc_hide_components/upload_outlined':
      return dynamic(() => import('./doc_hide_components/upload_outlined').then((mod) => mod.default));

    case 'doc_hide_components/previous_filled':
      return dynamic(() => import('./doc_hide_components/previous_filled').then((mod) => mod.default));

    case 'doc_hide_components/logo_text_en_filled':
      return dynamic(() => import('./doc_hide_components/logo_text_en_filled').then((mod) => mod.default));

    case 'doc_hide_components/logo_large_filled':
      return dynamic(() => import('./doc_hide_components/logo_large_filled').then((mod) => mod.default));

    case 'doc_hide_components/next_filled':
      return dynamic(() => import('./doc_hide_components/next_filled').then((mod) => mod.default));

    case 'doc_hide_components/logo_white_filled':
      return dynamic(() => import('./doc_hide_components/logo_white_filled').then((mod) => mod.default));

    case 'doc_hide_components/apilogotext__filled':
      return dynamic(() => import('./doc_hide_components/apilogotext__filled').then((mod) => mod.default));

    case 'doc_hide_components/comment_bj_entire_filled':
      return dynamic(() => import('./doc_hide_components/comment_bj_entire_filled').then((mod) => mod.default));

    case 'doc_hide_components/head_background_filled':
      return dynamic(() => import('./doc_hide_components/head_background_filled').then((mod) => mod.default));

    case 'doc_hide_components/solution_small_outlined':
      return dynamic(() => import('./doc_hide_components/solution_small_outlined').then((mod) => mod.default));

    case 'doc_hide_components/bug_outlined':
      return dynamic(() => import('./doc_hide_components/bug_outlined').then((mod) => mod.default));

    case 'doc_hide_components/apilogo__filled':
      return dynamic(() => import('./doc_hide_components/apilogo__filled').then((mod) => mod.default));

    case 'doc_hide_components/logotext_filled':
      return dynamic(() => import('./doc_hide_components/logotext_filled').then((mod) => mod.default));

    case 'doc_hide_components/logo_purple_filled':
      return dynamic(() => import('./doc_hide_components/logo_purple_filled').then((mod) => mod.default));

    case 'doc_hide_components/fitview_outlined':
      return dynamic(() => import('./doc_hide_components/fitview_outlined').then((mod) => mod.default));

    case 'doc_hide_components/vikaby_filled':
      return dynamic(() => import('./doc_hide_components/vikaby_filled').then((mod) => mod.default));

    case 'doc_hide_components/vikaby_outlined':
      return dynamic(() => import('./doc_hide_components/vikaby_outlined').then((mod) => mod.default));

    case 'doc_hide_components/comment_bg_filled':
      return dynamic(() => import('./doc_hide_components/comment_bg_filled').then((mod) => mod.default));

    case 'doc_hide_components/select_mark_filled':
      return dynamic(() => import('./doc_hide_components/select_mark_filled').then((mod) => mod.default));

    case 'doc_hide_components/linkedIn_filled':
      return dynamic(() => import('./doc_hide_components/linkedIn_filled').then((mod) => mod.default));

    case 'doc_hide_components/apple_filled':
      return dynamic(() => import('./doc_hide_components/apple_filled').then((mod) => mod.default));

    case 'doc_hide_components/google_filled':
      return dynamic(() => import('./doc_hide_components/google_filled').then((mod) => mod.default));

    case 'doc_hide_components/twitter_filled':
      return dynamic(() => import('./doc_hide_components/twitter_filled').then((mod) => mod.default));

    case 'doc_hide_components/github_filled':
      return dynamic(() => import('./doc_hide_components/github_filled').then((mod) => mod.default));

    case 'doc_hide_components/twitter_outlined':
      return dynamic(() => import('./doc_hide_components/twitter_outlined').then((mod) => mod.default));

    case 'doc_hide_components/linkedin_outlined':
      return dynamic(() => import('./doc_hide_components/linkedin_outlined').then((mod) => mod.default));

    case 'doc_hide_components/dingding_filled':
      return dynamic(() => import('./doc_hide_components/dingding_filled').then((mod) => mod.default));

    case 'doc_hide_components/feishu_color_filled':
      return dynamic(() => import('./doc_hide_components/feishu_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/qq_filled':
      return dynamic(() => import('./doc_hide_components/qq_filled').then((mod) => mod.default));

    case 'doc_hide_components/wechat_color_filled':
      return dynamic(() => import('./doc_hide_components/wechat_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/alipay_color_filled':
      return dynamic(() => import('./doc_hide_components/alipay_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/wecom_color_filled':
      return dynamic(() => import('./doc_hide_components/wecom_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/alipay_filled':
      return dynamic(() => import('./doc_hide_components/alipay_filled').then((mod) => mod.default));

    case 'doc_hide_components/dingding_color_filled':
      return dynamic(() => import('./doc_hide_components/dingding_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/feishu_filled':
      return dynamic(() => import('./doc_hide_components/feishu_filled').then((mod) => mod.default));

    case 'doc_hide_components/qq_color_filled':
      return dynamic(() => import('./doc_hide_components/qq_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/wechatpay_color_filled':
      return dynamic(() => import('./doc_hide_components/wechatpay_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/wechatpay_filled':
      return dynamic(() => import('./doc_hide_components/wechatpay_filled').then((mod) => mod.default));

    case 'doc_hide_components/wechat_filled':
      return dynamic(() => import('./doc_hide_components/wechat_filled').then((mod) => mod.default));

    case 'doc_hide_components/wecom_filled':
      return dynamic(() => import('./doc_hide_components/wecom_filled').then((mod) => mod.default));

    case 'doc_hide_components/emailfeedback_outlined':
      return dynamic(() => import('./doc_hide_components/emailfeedback_outlined').then((mod) => mod.default));

    case 'doc_hide_components/subtract_circle_color_filled':
      return dynamic(() => import('./doc_hide_components/subtract_circle_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/info_circle_color_filled':
      return dynamic(() => import('./doc_hide_components/info_circle_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/warn_circle_color_filled':
      return dynamic(() => import('./doc_hide_components/warn_circle_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/danger_circle_color_filled':
      return dynamic(() => import('./doc_hide_components/danger_circle_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/check_circle_color_filled':
      return dynamic(() => import('./doc_hide_components/check_circle_color_filled').then((mod) => mod.default));

    case 'doc_hide_components/liveChat_filled':
      return dynamic(() => import('./doc_hide_components/liveChat_filled').then((mod) => mod.default));

    case 'doc_hide_components/silver_dark_filled':
      return dynamic(() => import('./doc_hide_components/silver_dark_filled').then((mod) => mod.default));

    case 'doc_hide_components/enterprise_dark_filled':
      return dynamic(() => import('./doc_hide_components/enterprise_dark_filled').then((mod) => mod.default));

    case 'doc_hide_components/bronze_dark_filled':
      return dynamic(() => import('./doc_hide_components/bronze_dark_filled').then((mod) => mod.default));

    case 'doc_hide_components/gold_dark_filled':
      return dynamic(() => import('./doc_hide_components/gold_dark_filled').then((mod) => mod.default));

    case 'doc_hide_components/enterprise_light_filled':
      return dynamic(() => import('./doc_hide_components/enterprise_light_filled').then((mod) => mod.default));

    case 'doc_hide_components/gold_light_filled':
      return dynamic(() => import('./doc_hide_components/gold_light_filled').then((mod) => mod.default));

    case 'doc_hide_components/silver_light_filled':
      return dynamic(() => import('./doc_hide_components/silver_light_filled').then((mod) => mod.default));

    case 'doc_hide_components/bronze_light_filled':
      return dynamic(() => import('./doc_hide_components/bronze_light_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_line_stack_filled':
      return dynamic(() => import('./doc_hide_components/chart_line_stack_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_line_normal_filled':
      return dynamic(() => import('./doc_hide_components/chart_line_normal_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_line_percent_filled':
      return dynamic(() => import('./doc_hide_components/chart_line_percent_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_column_normal_filled':
      return dynamic(() => import('./doc_hide_components/chart_column_normal_filled').then((mod) => mod.default));

    case 'doc_hide_components/space_info_filled':
      return dynamic(() => import('./doc_hide_components/space_info_filled').then((mod) => mod.default));

    case 'doc_hide_components/webhook_filled':
      return dynamic(() => import('./doc_hide_components/webhook_filled').then((mod) => mod.default));

    case 'doc_hide_components/invite_box_filled':
      return dynamic(() => import('./doc_hide_components/invite_box_filled').then((mod) => mod.default));

    case 'doc_hide_components/bronze_filled':
      return dynamic(() => import('./doc_hide_components/bronze_filled').then((mod) => mod.default));

    case 'doc_hide_components/enterprise_filled':
      return dynamic(() => import('./doc_hide_components/enterprise_filled').then((mod) => mod.default));

    case 'doc_hide_components/gold_filled':
      return dynamic(() => import('./doc_hide_components/gold_filled').then((mod) => mod.default));

    case 'doc_hide_components/silver_filled':
      return dynamic(() => import('./doc_hide_components/silver_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_column_percent_filled':
      return dynamic(() => import('./doc_hide_components/chart_column_percent_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_pie_filled':
      return dynamic(() => import('./doc_hide_components/chart_pie_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_bar_stack_filled':
      return dynamic(() => import('./doc_hide_components/chart_bar_stack_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_bar_normal_filled':
      return dynamic(() => import('./doc_hide_components/chart_bar_normal_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_scatter_plotnormal_filled':
      return dynamic(() => import('./doc_hide_components/chart_scatter_plotnormal_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_bar_percent_filled':
      return dynamic(() => import('./doc_hide_components/chart_bar_percent_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_column_stack_filled':
      return dynamic(() => import('./doc_hide_components/chart_column_stack_filled').then((mod) => mod.default));

    case 'doc_hide_components/chart_dount_filled':
      return dynamic(() => import('./doc_hide_components/chart_dount_filled').then((mod) => mod.default));

    case 'doc_hide_components/embed_filled':
      return dynamic(() => import('./doc_hide_components/embed_filled').then((mod) => mod.default));

    case 'doc_hide_components/embed_outlined':
      return dynamic(() => import('./doc_hide_components/embed_outlined').then((mod) => mod.default));

    case 'doc_hide_components/cursor_button_filled':
      return dynamic(() => import('./doc_hide_components/cursor_button_filled').then((mod) => mod.default));

    case 'doc_hide_components/cursor_button_outlined':
      return dynamic(() => import('./doc_hide_components/cursor_button_outlined').then((mod) => mod.default));

    case 'doc_hide_components/placeholder_filled':
      return dynamic(() => import('./doc_hide_components/placeholder_filled').then((mod) => mod.default));

    case 'doc_hide_components/placeholder_outlined':
      return dynamic(() => import('./doc_hide_components/placeholder_outlined').then((mod) => mod.default));

    case 'doc_hide_components/one_way_link_filled':
      return dynamic(() => import('./doc_hide_components/one_way_link_filled').then((mod) => mod.default));

    case 'doc_hide_components/one_way_link_outlined':
      return dynamic(() => import('./doc_hide_components/one_way_link_outlined').then((mod) => mod.default));

    case 'doc_hide_components/two_way_link_filled':
      return dynamic(() => import('./doc_hide_components/two_way_link_filled').then((mod) => mod.default));

    case 'doc_hide_components/two_way_link_outlined':
      return dynamic(() => import('./doc_hide_components/two_way_link_outlined').then((mod) => mod.default));

    case 'doc_hide_components/Archive_outlined':
      return dynamic(() => import('./doc_hide_components/Archive_outlined').then((mod) => mod.default));

    case 'doc_hide_components/Archive_filled':
      return dynamic(() => import('./doc_hide_components/Archive_filled').then((mod) => mod.default));

    case 'doc_hide_components/dislike_outlined':
      return dynamic(() => import('./doc_hide_components/dislike_outlined').then((mod) => mod.default));

    case 'doc_hide_components/dislike_filled':
      return dynamic(() => import('./doc_hide_components/dislike_filled').then((mod) => mod.default));

    case 'doc_hide_components/toggle_outlined':
      return dynamic(() => import('./doc_hide_components/toggle_outlined').then((mod) => mod.default));

    case 'doc_hide_components/toggle_filled':
      return dynamic(() => import('./doc_hide_components/toggle_filled').then((mod) => mod.default));

    case 'doc_hide_components/refresh_filled':
      return dynamic(() => import('./doc_hide_components/refresh_filled').then((mod) => mod.default));

    case 'doc_hide_components/email_outlined':
      return dynamic(() => import('./doc_hide_components/email_outlined').then((mod) => mod.default));

    case 'doc_hide_components/user_role_filled':
      return dynamic(() => import('./doc_hide_components/user_role_filled').then((mod) => mod.default));

    case 'doc_hide_components/user_role_outlined':
      return dynamic(() => import('./doc_hide_components/user_role_outlined').then((mod) => mod.default));

    case 'doc_hide_components/train_filled':
      return dynamic(() => import('./doc_hide_components/train_filled').then((mod) => mod.default));

    case 'doc_hide_components/train_outlined':
      return dynamic(() => import('./doc_hide_components/train_outlined').then((mod) => mod.default));

    case 'doc_hide_components/up_and_down_outlined':
      return dynamic(() => import('./doc_hide_components/up_and_down_outlined').then((mod) => mod.default));

    case 'doc_hide_components/up_and_down_filled':
      return dynamic(() => import('./doc_hide_components/up_and_down_filled').then((mod) => mod.default));

    case 'doc_hide_components/enter_filled':
      return dynamic(() => import('./doc_hide_components/enter_filled').then((mod) => mod.default));

    case 'doc_hide_components/enter_outlined':
      return dynamic(() => import('./doc_hide_components/enter_outlined').then((mod) => mod.default));

    case 'doc_hide_components/time_outlined':
      return dynamic(() => import('./doc_hide_components/time_outlined').then((mod) => mod.default));

    case 'doc_hide_components/unchecked_outlined':
      return dynamic(() => import('./doc_hide_components/unchecked_outlined').then((mod) => mod.default));

    case 'doc_hide_components/unchecked_filled':
      return dynamic(() => import('./doc_hide_components/unchecked_filled').then((mod) => mod.default));

    case 'doc_hide_components/warn_circle_outlined':
      return dynamic(() => import('./doc_hide_components/warn_circle_outlined').then((mod) => mod.default));

    case 'doc_hide_components/warn_circle_filled':
      return dynamic(() => import('./doc_hide_components/warn_circle_filled').then((mod) => mod.default));

    case 'doc_hide_components/organizational_outlined':
      return dynamic(() => import('./doc_hide_components/organizational_outlined').then((mod) => mod.default));

    case 'doc_hide_components/organizational_filled':
      return dynamic(() => import('./doc_hide_components/organizational_filled').then((mod) => mod.default));

    case 'doc_hide_components/notification_check_outlined':
      return dynamic(() => import('./doc_hide_components/notification_check_outlined').then((mod) => mod.default));

    case 'doc_hide_components/notification_check_filled':
      return dynamic(() => import('./doc_hide_components/notification_check_filled').then((mod) => mod.default));

    case 'doc_hide_components/grid_mirror_outlined':
      return dynamic(() => import('./doc_hide_components/grid_mirror_outlined').then((mod) => mod.default));

    case 'doc_hide_components/architecture_mirror_outlined':
      return dynamic(() => import('./doc_hide_components/architecture_mirror_outlined').then((mod) => mod.default));

    case 'doc_hide_components/kanban_filled':
      return dynamic(() => import('./doc_hide_components/kanban_filled').then((mod) => mod.default));

    case 'doc_hide_components/architecture_filled':
      return dynamic(() => import('./doc_hide_components/architecture_filled').then((mod) => mod.default));

    case 'doc_hide_components/gantt_mirror_outlined':
      return dynamic(() => import('./doc_hide_components/gantt_mirror_outlined').then((mod) => mod.default));

    case 'doc_hide_components/kanban_mirror_outlined':
      return dynamic(() => import('./doc_hide_components/kanban_mirror_outlined').then((mod) => mod.default));

    case 'doc_hide_components/gallery_mirror_outlined':
      return dynamic(() => import('./doc_hide_components/gallery_mirror_outlined').then((mod) => mod.default));

    case 'doc_hide_components/gallery_mirror_filled':
      return dynamic(() => import('./doc_hide_components/gallery_mirror_filled').then((mod) => mod.default));

    case 'doc_hide_components/calendar_mirror_outlined':
      return dynamic(() => import('./doc_hide_components/calendar_mirror_outlined').then((mod) => mod.default));

    case 'doc_hide_components/calendar_mirror_filled':
      return dynamic(() => import('./doc_hide_components/calendar_mirror_filled').then((mod) => mod.default));

    case 'doc_hide_components/architecture_mirror_filled':
      return dynamic(() => import('./doc_hide_components/architecture_mirror_filled').then((mod) => mod.default));

    case 'doc_hide_components/gantt_mirror_filled':
      return dynamic(() => import('./doc_hide_components/gantt_mirror_filled').then((mod) => mod.default));

    case 'doc_hide_components/kanban_mirror_filled':
      return dynamic(() => import('./doc_hide_components/kanban_mirror_filled').then((mod) => mod.default));

    case 'doc_hide_components/grid_mirror_filled':
      return dynamic(() => import('./doc_hide_components/grid_mirror_filled').then((mod) => mod.default));

    case 'doc_hide_components/gantt_filled':
      return dynamic(() => import('./doc_hide_components/gantt_filled').then((mod) => mod.default));

    case 'doc_hide_components/architecture_outlined':
      return dynamic(() => import('./doc_hide_components/architecture_outlined').then((mod) => mod.default));

    case 'doc_hide_components/gantt_outlined':
      return dynamic(() => import('./doc_hide_components/gantt_outlined').then((mod) => mod.default));

    case 'doc_hide_components/kanban_outlined':
      return dynamic(() => import('./doc_hide_components/kanban_outlined').then((mod) => mod.default));

    case 'doc_hide_components/italics_filled':
      return dynamic(() => import('./doc_hide_components/italics_filled').then((mod) => mod.default));

    case 'doc_hide_components/body_outlined':
      return dynamic(() => import('./doc_hide_components/body_outlined').then((mod) => mod.default));

    case 'doc_hide_components/headline_2_outlined':
      return dynamic(() => import('./doc_hide_components/headline_2_outlined').then((mod) => mod.default));

    case 'doc_hide_components/headline_1_outlined':
      return dynamic(() => import('./doc_hide_components/headline_1_outlined').then((mod) => mod.default));

    case 'doc_hide_components/ordered_outlined':
      return dynamic(() => import('./doc_hide_components/ordered_outlined').then((mod) => mod.default));

    case 'doc_hide_components/italics_outlined':
      return dynamic(() => import('./doc_hide_components/italics_outlined').then((mod) => mod.default));

    case 'doc_hide_components/unordered_outlined':
      return dynamic(() => import('./doc_hide_components/unordered_outlined').then((mod) => mod.default));

    case 'doc_hide_components/org_zoom_out_outlined':
      return dynamic(() => import('./doc_hide_components/org_zoom_out_outlined').then((mod) => mod.default));

    case 'doc_hide_components/task_list_outlined':
      return dynamic(() => import('./doc_hide_components/task_list_outlined').then((mod) => mod.default));

    case 'doc_hide_components/code_outlined':
      return dynamic(() => import('./doc_hide_components/code_outlined').then((mod) => mod.default));

    case 'doc_hide_components/headline_3_outlined':
      return dynamic(() => import('./doc_hide_components/headline_3_outlined').then((mod) => mod.default));

    case 'doc_hide_components/text_right_outlined':
      return dynamic(() => import('./doc_hide_components/text_right_outlined').then((mod) => mod.default));

    case 'doc_hide_components/underline_outlined':
      return dynamic(() => import('./doc_hide_components/underline_outlined').then((mod) => mod.default));

    case 'doc_hide_components/strikethrough_outlined':
      return dynamic(() => import('./doc_hide_components/strikethrough_outlined').then((mod) => mod.default));

    case 'doc_hide_components/bold_outlined':
      return dynamic(() => import('./doc_hide_components/bold_outlined').then((mod) => mod.default));

    case 'doc_hide_components/text_middle_outlined':
      return dynamic(() => import('./doc_hide_components/text_middle_outlined').then((mod) => mod.default));

    case 'doc_hide_components/text_left_outlined':
      return dynamic(() => import('./doc_hide_components/text_left_outlined').then((mod) => mod.default));

    case 'doc_hide_components/quote_outlined':
      return dynamic(() => import('./doc_hide_components/quote_outlined').then((mod) => mod.default));

    case 'doc_hide_components/dividing_line_outlined':
      return dynamic(() => import('./doc_hide_components/dividing_line_outlined').then((mod) => mod.default));

    case 'doc_hide_components/highlight_outlined':
      return dynamic(() => import('./doc_hide_components/highlight_outlined').then((mod) => mod.default));

    case 'doc_hide_components/bold_filled':
      return dynamic(() => import('./doc_hide_components/bold_filled').then((mod) => mod.default));

    case 'doc_hide_components/text_left_filled':
      return dynamic(() => import('./doc_hide_components/text_left_filled').then((mod) => mod.default));

    case 'doc_hide_components/body_filled':
      return dynamic(() => import('./doc_hide_components/body_filled').then((mod) => mod.default));

    case 'doc_hide_components/code_filled':
      return dynamic(() => import('./doc_hide_components/code_filled').then((mod) => mod.default));

    case 'doc_hide_components/org_zoom_out_filled':
      return dynamic(() => import('./doc_hide_components/org_zoom_out_filled').then((mod) => mod.default));

    case 'doc_hide_components/quote_filled':
      return dynamic(() => import('./doc_hide_components/quote_filled').then((mod) => mod.default));

    case 'doc_hide_components/dividing_line_filled':
      return dynamic(() => import('./doc_hide_components/dividing_line_filled').then((mod) => mod.default));

    case 'doc_hide_components/underline_filled':
      return dynamic(() => import('./doc_hide_components/underline_filled').then((mod) => mod.default));

    case 'doc_hide_components/text_middle_filled':
      return dynamic(() => import('./doc_hide_components/text_middle_filled').then((mod) => mod.default));

    case 'doc_hide_components/headline_1_filled':
      return dynamic(() => import('./doc_hide_components/headline_1_filled').then((mod) => mod.default));

    case 'doc_hide_components/strikethrough_filled':
      return dynamic(() => import('./doc_hide_components/strikethrough_filled').then((mod) => mod.default));

    case 'doc_hide_components/text_right_filled':
      return dynamic(() => import('./doc_hide_components/text_right_filled').then((mod) => mod.default));

    case 'doc_hide_components/task_list_filled':
      return dynamic(() => import('./doc_hide_components/task_list_filled').then((mod) => mod.default));

    case 'doc_hide_components/highlight_filled':
      return dynamic(() => import('./doc_hide_components/highlight_filled').then((mod) => mod.default));

    case 'doc_hide_components/unordered_filled':
      return dynamic(() => import('./doc_hide_components/unordered_filled').then((mod) => mod.default));

    case 'doc_hide_components/headline_3_filled':
      return dynamic(() => import('./doc_hide_components/headline_3_filled').then((mod) => mod.default));

    case 'doc_hide_components/ordered_filled':
      return dynamic(() => import('./doc_hide_components/ordered_filled').then((mod) => mod.default));

    case 'doc_hide_components/headline_2_filled':
      return dynamic(() => import('./doc_hide_components/headline_2_filled').then((mod) => mod.default));

    case 'components/automation_outlined':
      return dynamic(() => import('./components/automation_outlined').then((mod) => mod.default));

    case 'components/automation_filled':
      return dynamic(() => import('./components/automation_filled').then((mod) => mod.default));

    case 'components/refresh_outlined':
      return dynamic(() => import('./components/refresh_outlined').then((mod) => mod.default));

    case 'components/paste_filled':
      return dynamic(() => import('./components/paste_filled').then((mod) => mod.default));

    case 'components/esc_outlined':
      return dynamic(() => import('./components/esc_outlined').then((mod) => mod.default));

    case 'components/esc_filled':
      return dynamic(() => import('./components/esc_filled').then((mod) => mod.default));

    case 'components/log_outlined':
      return dynamic(() => import('./components/log_outlined').then((mod) => mod.default));

    case 'components/log_filled':
      return dynamic(() => import('./components/log_filled').then((mod) => mod.default));

    case 'components/style_outlined':
      return dynamic(() => import('./components/style_outlined').then((mod) => mod.default));

    case 'components/style_filled':
      return dynamic(() => import('./components/style_filled').then((mod) => mod.default));

    case 'components/loading_filled':
      return dynamic(() => import('./components/loading_filled').then((mod) => mod.default));

    case 'components/loading_outlined':
      return dynamic(() => import('./components/loading_outlined').then((mod) => mod.default));

    case 'components/shield_security_filled':
      return dynamic(() => import('./components/shield_security_filled').then((mod) => mod.default));

    case 'components/shield_security_outlined':
      return dynamic(() => import('./components/shield_security_outlined').then((mod) => mod.default));

    case 'components/compass_outlined':
      return dynamic(() => import('./components/compass_outlined').then((mod) => mod.default));

    case 'components/compass_filled':
      return dynamic(() => import('./components/compass_filled').then((mod) => mod.default));

    case 'components/setting_2_outlined':
      return dynamic(() => import('./components/setting_2_outlined').then((mod) => mod.default));

    case 'components/setting_2_filled':
      return dynamic(() => import('./components/setting_2_filled').then((mod) => mod.default));

    case 'components/currency_USD_filled':
      return dynamic(() => import('./components/currency_USD_filled').then((mod) => mod.default));

    case 'components/adjustment_outlined':
      return dynamic(() => import('./components/adjustment_outlined').then((mod) => mod.default));

    case 'components/question_circle_filled':
      return dynamic(() => import('./components/question_circle_filled').then((mod) => mod.default));

    case 'components/question_circle_outlined':
      return dynamic(() => import('./components/question_circle_outlined').then((mod) => mod.default));

    case 'components/info_circle_outlined':
      return dynamic(() => import('./components/info_circle_outlined').then((mod) => mod.default));

    case 'components/description_outlined':
      return dynamic(() => import('./components/description_outlined').then((mod) => mod.default));

    case 'components/description_filled':
      return dynamic(() => import('./components/description_filled').then((mod) => mod.default));

    case 'components/form_add_filled':
      return dynamic(() => import('./components/form_add_filled').then((mod) => mod.default));

    case 'components/info_filled':
      return dynamic(() => import('./components/info_filled').then((mod) => mod.default));

    case 'components/user_admin_outlined':
      return dynamic(() => import('./components/user_admin_outlined').then((mod) => mod.default));

    case 'components/user_admin_filled':
      return dynamic(() => import('./components/user_admin_filled').then((mod) => mod.default));

    case 'components/star_2_filled':
      return dynamic(() => import('./components/star_2_filled').then((mod) => mod.default));

    case 'components/star_2_outlined':
      return dynamic(() => import('./components/star_2_outlined').then((mod) => mod.default));

    case 'components/bank_outlined':
      return dynamic(() => import('./components/bank_outlined').then((mod) => mod.default));

    case 'components/conical_right_outlined':
      return dynamic(() => import('./components/conical_right_outlined').then((mod) => mod.default));

    case 'components/conical_left_outlined':
      return dynamic(() => import('./components/conical_left_outlined').then((mod) => mod.default));

    case 'components/conical_down_outlined':
      return dynamic(() => import('./components/conical_down_outlined').then((mod) => mod.default));

    case 'components/conical_right_filled':
      return dynamic(() => import('./components/conical_right_filled').then((mod) => mod.default));

    case 'components/conical_left_filled':
      return dynamic(() => import('./components/conical_left_filled').then((mod) => mod.default));

    case 'components/conical_down_filled':
      return dynamic(() => import('./components/conical_down_filled').then((mod) => mod.default));

    case 'components/conical_up_filled':
      return dynamic(() => import('./components/conical_up_filled').then((mod) => mod.default));

    case 'components/conical_up_outlined':
      return dynamic(() => import('./components/conical_up_outlined').then((mod) => mod.default));

    case 'components/transfer_filled':
      return dynamic(() => import('./components/transfer_filled').then((mod) => mod.default));

    case 'components/transfer_outlined':
      return dynamic(() => import('./components/transfer_outlined').then((mod) => mod.default));

    case 'components/question_outlined':
      return dynamic(() => import('./components/question_outlined').then((mod) => mod.default));

    case 'components/warn_filled':
      return dynamic(() => import('./components/warn_filled').then((mod) => mod.default));

    case 'components/question_filled':
      return dynamic(() => import('./components/question_filled').then((mod) => mod.default));

    case 'components/warn_outlined':
      return dynamic(() => import('./components/warn_outlined').then((mod) => mod.default));

    case 'components/info_outlined':
      return dynamic(() => import('./components/info_outlined').then((mod) => mod.default));

    case 'components/sidescreen_filled':
      return dynamic(() => import('./components/sidescreen_filled').then((mod) => mod.default));

    case 'components/middlescreen_filled':
      return dynamic(() => import('./components/middlescreen_filled').then((mod) => mod.default));

    case 'components/middlescreen_outlined':
      return dynamic(() => import('./components/middlescreen_outlined').then((mod) => mod.default));

    case 'components/sidescreen_outlined':
      return dynamic(() => import('./components/sidescreen_outlined').then((mod) => mod.default));

    case 'components/bank_filled':
      return dynamic(() => import('./components/bank_filled').then((mod) => mod.default));

    case 'components/bulb_filled':
      return dynamic(() => import('./components/bulb_filled').then((mod) => mod.default));

    case 'components/bulb_outlined':
      return dynamic(() => import('./components/bulb_outlined').then((mod) => mod.default));

    case 'components/rocket_filled':
      return dynamic(() => import('./components/rocket_filled').then((mod) => mod.default));

    case 'components/rocket_outlined':
      return dynamic(() => import('./components/rocket_outlined').then((mod) => mod.default));

    case 'components/network_normal_filled':
      return dynamic(() => import('./components/network_normal_filled').then((mod) => mod.default));

    case 'components/network_connect_filled':
      return dynamic(() => import('./components/network_connect_filled').then((mod) => mod.default));

    case 'components/network_error_filled':
      return dynamic(() => import('./components/network_error_filled').then((mod) => mod.default));

    case 'components/network_error_outlined':
      return dynamic(() => import('./components/network_error_outlined').then((mod) => mod.default));

    case 'components/network_normal_outlined':
      return dynamic(() => import('./components/network_normal_outlined').then((mod) => mod.default));

    case 'components/network_connect_outlined':
      return dynamic(() => import('./components/network_connect_outlined').then((mod) => mod.default));

    case 'components/stopwatch_filled':
      return dynamic(() => import('./components/stopwatch_filled').then((mod) => mod.default));

    case 'components/keyboard_outlined':
      return dynamic(() => import('./components/keyboard_outlined').then((mod) => mod.default));

    case 'components/keyboard_filled':
      return dynamic(() => import('./components/keyboard_filled').then((mod) => mod.default));

    case 'components/book_filled':
      return dynamic(() => import('./components/book_filled').then((mod) => mod.default));

    case 'components/book_outlined':
      return dynamic(() => import('./components/book_outlined').then((mod) => mod.default));

    case 'components/workbench_filled':
      return dynamic(() => import('./components/workbench_filled').then((mod) => mod.default));

    case 'components/stopwatch_outlined':
      return dynamic(() => import('./components/stopwatch_outlined').then((mod) => mod.default));

    case 'components/workbench_outlined':
      return dynamic(() => import('./components/workbench_outlined').then((mod) => mod.default));

    case 'components/attention_outlined':
      return dynamic(() => import('./components/attention_outlined').then((mod) => mod.default));

    case 'components/unpublish_filled':
      return dynamic(() => import('./components/unpublish_filled').then((mod) => mod.default));

    case 'components/publish_filled':
      return dynamic(() => import('./components/publish_filled').then((mod) => mod.default));

    case 'components/unpublish_outlined':
      return dynamic(() => import('./components/unpublish_outlined').then((mod) => mod.default));

    case 'components/publish_outlined':
      return dynamic(() => import('./components/publish_outlined').then((mod) => mod.default));

    case 'components/file_search_outlined':
      return dynamic(() => import('./components/file_search_outlined').then((mod) => mod.default));

    case 'components/file_search_filled':
      return dynamic(() => import('./components/file_search_filled').then((mod) => mod.default));

    case 'components/collapse_3_filled':
      return dynamic(() => import('./components/collapse_3_filled').then((mod) => mod.default));

    case 'components/collapse_3_open_filled':
      return dynamic(() => import('./components/collapse_3_open_filled').then((mod) => mod.default));

    case 'components/attention_filled':
      return dynamic(() => import('./components/attention_filled').then((mod) => mod.default));

    case 'components/like_outlined':
      return dynamic(() => import('./components/like_outlined').then((mod) => mod.default));

    case 'components/like_filled':
      return dynamic(() => import('./components/like_filled').then((mod) => mod.default));

    case 'components/service_outlined':
      return dynamic(() => import('./components/service_outlined').then((mod) => mod.default));

    case 'components/clear_filled':
      return dynamic(() => import('./components/clear_filled').then((mod) => mod.default));

    case 'components/clear_outlined':
      return dynamic(() => import('./components/clear_outlined').then((mod) => mod.default));

    case 'components/service_filled':
      return dynamic(() => import('./components/service_filled').then((mod) => mod.default));

    case 'components/link_disconnect_filled':
      return dynamic(() => import('./components/link_disconnect_filled').then((mod) => mod.default));

    case 'components/link_disconnect_outlined':
      return dynamic(() => import('./components/link_disconnect_outlined').then((mod) => mod.default));

    case 'components/download_filled':
      return dynamic(() => import('./components/download_filled').then((mod) => mod.default));

    case 'components/download_outlined':
      return dynamic(() => import('./components/download_outlined').then((mod) => mod.default));

    case 'components/rotate_outlined':
      return dynamic(() => import('./components/rotate_outlined').then((mod) => mod.default));

    case 'components/rotate_filled':
      return dynamic(() => import('./components/rotate_filled').then((mod) => mod.default));

    case 'components/calendar_filled':
      return dynamic(() => import('./components/calendar_filled').then((mod) => mod.default));

    case 'components/grid_filled':
      return dynamic(() => import('./components/grid_filled').then((mod) => mod.default));

    case 'components/gallery_filled':
      return dynamic(() => import('./components/gallery_filled').then((mod) => mod.default));

    case 'components/gallery_outlined':
      return dynamic(() => import('./components/gallery_outlined').then((mod) => mod.default));

    case 'components/calendar_outlined':
      return dynamic(() => import('./components/calendar_outlined').then((mod) => mod.default));

    case 'components/grid_outlined':
      return dynamic(() => import('./components/grid_outlined').then((mod) => mod.default));

    case 'components/form_outlined':
      return dynamic(() => import('./components/form_outlined').then((mod) => mod.default));

    case 'components/form_filled':
      return dynamic(() => import('./components/form_filled').then((mod) => mod.default));

    case 'components/form_add_outlined':
      return dynamic(() => import('./components/form_add_outlined').then((mod) => mod.default));

    case 'components/chevron_left_outlined':
      return dynamic(() => import('./components/chevron_left_outlined').then((mod) => mod.default));

    case 'components/image_outlined':
      return dynamic(() => import('./components/image_outlined').then((mod) => mod.default));

    case 'components/delete_filled':
      return dynamic(() => import('./components/delete_filled').then((mod) => mod.default));

    case 'components/delete_outlined':
      return dynamic(() => import('./components/delete_outlined').then((mod) => mod.default));

    case 'components/file_excel_filled':
      return dynamic(() => import('./components/file_excel_filled').then((mod) => mod.default));

    case 'components/file_filled':
      return dynamic(() => import('./components/file_filled').then((mod) => mod.default));

    case 'components/folder_right_filled':
      return dynamic(() => import('./components/folder_right_filled').then((mod) => mod.default));

    case 'components/folder_open_filled':
      return dynamic(() => import('./components/folder_open_filled').then((mod) => mod.default));

    case 'components/folder_open_outlined':
      return dynamic(() => import('./components/folder_open_outlined').then((mod) => mod.default));

    case 'components/folder_empty_filled':
      return dynamic(() => import('./components/folder_empty_filled').then((mod) => mod.default));

    case 'components/folder_empty_outlined':
      return dynamic(() => import('./components/folder_empty_outlined').then((mod) => mod.default));

    case 'components/folder_normal_filled':
      return dynamic(() => import('./components/folder_normal_filled').then((mod) => mod.default));

    case 'components/folder_normal_outlined':
      return dynamic(() => import('./components/folder_normal_outlined').then((mod) => mod.default));

    case 'components/file_outlined':
      return dynamic(() => import('./components/file_outlined').then((mod) => mod.default));

    case 'components/datasheet_filled':
      return dynamic(() => import('./components/datasheet_filled').then((mod) => mod.default));

    case 'components/file_add_filled':
      return dynamic(() => import('./components/file_add_filled').then((mod) => mod.default));

    case 'components/file_csv_filled':
      return dynamic(() => import('./components/file_csv_filled').then((mod) => mod.default));

    case 'components/folder_add_filled':
      return dynamic(() => import('./components/folder_add_filled').then((mod) => mod.default));

    case 'components/folder_down_filled':
      return dynamic(() => import('./components/folder_down_filled').then((mod) => mod.default));

    case 'components/folder_up_filled':
      return dynamic(() => import('./components/folder_up_filled').then((mod) => mod.default));

    case 'components/folder_left_filled':
      return dynamic(() => import('./components/folder_left_filled').then((mod) => mod.default));

    case 'components/folder_down_outlined':
      return dynamic(() => import('./components/folder_down_outlined').then((mod) => mod.default));

    case 'components/folder_up_outlined':
      return dynamic(() => import('./components/folder_up_outlined').then((mod) => mod.default));

    case 'components/folder_left_outlined':
      return dynamic(() => import('./components/folder_left_outlined').then((mod) => mod.default));

    case 'components/datasheet_outlined':
      return dynamic(() => import('./components/datasheet_outlined').then((mod) => mod.default));

    case 'components/folder_right_outlined':
      return dynamic(() => import('./components/folder_right_outlined').then((mod) => mod.default));

    case 'components/file_csv_outlined':
      return dynamic(() => import('./components/file_csv_outlined').then((mod) => mod.default));

    case 'components/file_excel_outlined':
      return dynamic(() => import('./components/file_excel_outlined').then((mod) => mod.default));

    case 'components/file_add_outlined':
      return dynamic(() => import('./components/file_add_outlined').then((mod) => mod.default));

    case 'components/folder_add_outlined':
      return dynamic(() => import('./components/folder_add_outlined').then((mod) => mod.default));

    case 'components/user_add_filled':
      return dynamic(() => import('./components/user_add_filled').then((mod) => mod.default));

    case 'components/user_edit_filled':
      return dynamic(() => import('./components/user_edit_filled').then((mod) => mod.default));

    case 'components/user_filled':
      return dynamic(() => import('./components/user_filled').then((mod) => mod.default));

    case 'components/user_group_filled':
      return dynamic(() => import('./components/user_group_filled').then((mod) => mod.default));

    case 'components/user_edit_outlined':
      return dynamic(() => import('./components/user_edit_outlined').then((mod) => mod.default));

    case 'components/user_outlined':
      return dynamic(() => import('./components/user_outlined').then((mod) => mod.default));

    case 'components/user_add_outlined':
      return dynamic(() => import('./components/user_add_outlined').then((mod) => mod.default));

    case 'components/user_group_outlined':
      return dynamic(() => import('./components/user_group_outlined').then((mod) => mod.default));

    case 'components/list_filled':
      return dynamic(() => import('./components/list_filled').then((mod) => mod.default));

    case 'components/cascade_filled':
      return dynamic(() => import('./components/cascade_filled').then((mod) => mod.default));

    case 'components/close_circle_outlined':
      return dynamic(() => import('./components/close_circle_outlined').then((mod) => mod.default));

    case 'components/check_circle_outlined':
      return dynamic(() => import('./components/check_circle_outlined').then((mod) => mod.default));

    case 'components/close_circle_filled':
      return dynamic(() => import('./components/close_circle_filled').then((mod) => mod.default));

    case 'components/info_circle_filled':
      return dynamic(() => import('./components/info_circle_filled').then((mod) => mod.default));

    case 'components/check_circle_filled':
      return dynamic(() => import('./components/check_circle_filled').then((mod) => mod.default));

    case 'components/star_cross_outlined':
      return dynamic(() => import('./components/star_cross_outlined').then((mod) => mod.default));

    case 'components/autonumber_filled':
      return dynamic(() => import('./components/autonumber_filled').then((mod) => mod.default));

    case 'components/satellite_filled':
      return dynamic(() => import('./components/satellite_filled').then((mod) => mod.default));

    case 'components/emoji_filled':
      return dynamic(() => import('./components/emoji_filled').then((mod) => mod.default));

    case 'components/home_outlined':
      return dynamic(() => import('./components/home_outlined').then((mod) => mod.default));

    case 'components/telephone_outlined':
      return dynamic(() => import('./components/telephone_outlined').then((mod) => mod.default));

    case 'components/telephone_filled':
      return dynamic(() => import('./components/telephone_filled').then((mod) => mod.default));

    case 'components/notification_filled':
      return dynamic(() => import('./components/notification_filled').then((mod) => mod.default));

    case 'components/rowhight_medium_filled':
      return dynamic(() => import('./components/rowhight_medium_filled').then((mod) => mod.default));

    case 'components/qrcode_filled':
      return dynamic(() => import('./components/qrcode_filled').then((mod) => mod.default));

    case 'components/gift_filled':
      return dynamic(() => import('./components/gift_filled').then((mod) => mod.default));

    case 'components/shield_check_filled':
      return dynamic(() => import('./components/shield_check_filled').then((mod) => mod.default));

    case 'components/mortarboard_filled':
      return dynamic(() => import('./components/mortarboard_filled').then((mod) => mod.default));

    case 'components/linktable_filled':
      return dynamic(() => import('./components/linktable_filled').then((mod) => mod.default));

    case 'components/robot_filled':
      return dynamic(() => import('./components/robot_filled').then((mod) => mod.default));

    case 'components/time_filled':
      return dynamic(() => import('./components/time_filled').then((mod) => mod.default));

    case 'components/mobilephone_filled':
      return dynamic(() => import('./components/mobilephone_filled').then((mod) => mod.default));

    case 'components/longtext_filled':
      return dynamic(() => import('./components/longtext_filled').then((mod) => mod.default));

    case 'components/select_single_filled':
      return dynamic(() => import('./components/select_single_filled').then((mod) => mod.default));

    case 'components/alarm_filled':
      return dynamic(() => import('./components/alarm_filled').then((mod) => mod.default));

    case 'components/rowhight_medium_outlined':
      return dynamic(() => import('./components/rowhight_medium_outlined').then((mod) => mod.default));

    case 'components/rowhight_short_outlined':
      return dynamic(() => import('./components/rowhight_short_outlined').then((mod) => mod.default));

    case 'components/longtext_outlined':
      return dynamic(() => import('./components/longtext_outlined').then((mod) => mod.default));

    case 'components/mobilephone_outlined':
      return dynamic(() => import('./components/mobilephone_outlined').then((mod) => mod.default));

    case 'components/satellite_outlined':
      return dynamic(() => import('./components/satellite_outlined').then((mod) => mod.default));

    case 'components/horn_outlined':
      return dynamic(() => import('./components/horn_outlined').then((mod) => mod.default));

    case 'components/more_stand_outlined':
      return dynamic(() => import('./components/more_stand_outlined').then((mod) => mod.default));

    case 'components/link_outlined':
      return dynamic(() => import('./components/link_outlined').then((mod) => mod.default));

    case 'components/pin_outlined':
      return dynamic(() => import('./components/pin_outlined').then((mod) => mod.default));

    case 'components/add_circle_outlined':
      return dynamic(() => import('./components/add_circle_outlined').then((mod) => mod.default));

    case 'components/community_filled':
      return dynamic(() => import('./components/community_filled').then((mod) => mod.default));

    case 'components/community_outlined':
      return dynamic(() => import('./components/community_outlined').then((mod) => mod.default));

    case 'components/manage_application_filled':
      return dynamic(() => import('./components/manage_application_filled').then((mod) => mod.default));

    case 'components/manage_application_outlined':
      return dynamic(() => import('./components/manage_application_outlined').then((mod) => mod.default));

    case 'components/department_outlined':
      return dynamic(() => import('./components/department_outlined').then((mod) => mod.default));

    case 'components/department_filled':
      return dynamic(() => import('./components/department_filled').then((mod) => mod.default));

    case 'components/home_filled':
      return dynamic(() => import('./components/home_filled').then((mod) => mod.default));

    case 'components/planet_outlined':
      return dynamic(() => import('./components/planet_outlined').then((mod) => mod.default));

    case 'components/planet_filled':
      return dynamic(() => import('./components/planet_filled').then((mod) => mod.default));

    case 'components/star_cross_filled':
      return dynamic(() => import('./components/star_cross_filled').then((mod) => mod.default));

    case 'components/advise_outlined':
      return dynamic(() => import('./components/advise_outlined').then((mod) => mod.default));

    case 'components/list_outlined':
      return dynamic(() => import('./components/list_outlined').then((mod) => mod.default));

    case 'components/advise_filled':
      return dynamic(() => import('./components/advise_filled').then((mod) => mod.default));

    case 'components/widget_filled':
      return dynamic(() => import('./components/widget_filled').then((mod) => mod.default));

    case 'components/autonumber_outlined':
      return dynamic(() => import('./components/autonumber_outlined').then((mod) => mod.default));

    case 'components/history_outlined':
      return dynamic(() => import('./components/history_outlined').then((mod) => mod.default));

    case 'components/video_filled':
      return dynamic(() => import('./components/video_filled').then((mod) => mod.default));

    case 'components/video_outlined':
      return dynamic(() => import('./components/video_outlined').then((mod) => mod.default));

    case 'components/currency_USD_outlined':
      return dynamic(() => import('./components/currency_USD_outlined').then((mod) => mod.default));

    case 'components/roadmap_filled':
      return dynamic(() => import('./components/roadmap_filled').then((mod) => mod.default));

    case 'components/roadmap_outlined':
      return dynamic(() => import('./components/roadmap_outlined').then((mod) => mod.default));

    case 'components/history_filled':
      return dynamic(() => import('./components/history_filled').then((mod) => mod.default));

    case 'components/checkbox_filled':
      return dynamic(() => import('./components/checkbox_filled').then((mod) => mod.default));

    case 'components/number_filled':
      return dynamic(() => import('./components/number_filled').then((mod) => mod.default));

    case 'components/attachment_filled':
      return dynamic(() => import('./components/attachment_filled').then((mod) => mod.default));

    case 'components/rowhight_extremhigh_filled':
      return dynamic(() => import('./components/rowhight_extremhigh_filled').then((mod) => mod.default));

    case 'components/rowhight_high_filled':
      return dynamic(() => import('./components/rowhight_high_filled').then((mod) => mod.default));

    case 'components/link_filled':
      return dynamic(() => import('./components/link_filled').then((mod) => mod.default));

    case 'components/shield_add_outlined':
      return dynamic(() => import('./components/shield_add_outlined').then((mod) => mod.default));

    case 'components/horn_filled':
      return dynamic(() => import('./components/horn_filled').then((mod) => mod.default));

    case 'components/class_filled':
      return dynamic(() => import('./components/class_filled').then((mod) => mod.default));

    case 'components/desktop_filled':
      return dynamic(() => import('./components/desktop_filled').then((mod) => mod.default));

    case 'components/shield_add_filled':
      return dynamic(() => import('./components/shield_add_filled').then((mod) => mod.default));

    case 'components/more_filled':
      return dynamic(() => import('./components/more_filled').then((mod) => mod.default));

    case 'components/more_stand_filled':
      return dynamic(() => import('./components/more_stand_filled').then((mod) => mod.default));

    case 'components/pin_filled':
      return dynamic(() => import('./components/pin_filled').then((mod) => mod.default));

    case 'components/email_filled':
      return dynamic(() => import('./components/email_filled').then((mod) => mod.default));

    case 'components/mirror_filled':
      return dynamic(() => import('./components/mirror_filled').then((mod) => mod.default));

    case 'components/api_filled':
      return dynamic(() => import('./components/api_filled').then((mod) => mod.default));

    case 'components/rowhight_short_filled':
      return dynamic(() => import('./components/rowhight_short_filled').then((mod) => mod.default));

    case 'components/web_filled':
      return dynamic(() => import('./components/web_filled').then((mod) => mod.default));

    case 'components/formula_filled':
      return dynamic(() => import('./components/formula_filled').then((mod) => mod.default));

    case 'components/percent_filled':
      return dynamic(() => import('./components/percent_filled').then((mod) => mod.default));

    case 'components/currency_CNY_filled':
      return dynamic(() => import('./components/currency_CNY_filled').then((mod) => mod.default));

    case 'components/select_multiple_filled':
      return dynamic(() => import('./components/select_multiple_filled').then((mod) => mod.default));

    case 'components/text_filled':
      return dynamic(() => import('./components/text_filled').then((mod) => mod.default));

    case 'components/image_filled':
      return dynamic(() => import('./components/image_filled').then((mod) => mod.default));

    case 'components/web_outlined':
      return dynamic(() => import('./components/web_outlined').then((mod) => mod.default));

    case 'components/emoji_outlined':
      return dynamic(() => import('./components/emoji_outlined').then((mod) => mod.default));

    case 'components/mirror_outlined':
      return dynamic(() => import('./components/mirror_outlined').then((mod) => mod.default));

    case 'components/robot_outlined':
      return dynamic(() => import('./components/robot_outlined').then((mod) => mod.default));

    case 'components/api_outlined':
      return dynamic(() => import('./components/api_outlined').then((mod) => mod.default));

    case 'components/widget_outlined':
      return dynamic(() => import('./components/widget_outlined').then((mod) => mod.default));

    case 'components/rowhight_extremhigh_outlined':
      return dynamic(() => import('./components/rowhight_extremhigh_outlined').then((mod) => mod.default));

    case 'components/rowhight_high_outlined':
      return dynamic(() => import('./components/rowhight_high_outlined').then((mod) => mod.default));

    case 'components/cascade_outlined':
      return dynamic(() => import('./components/cascade_outlined').then((mod) => mod.default));

    case 'components/linktable_outlined':
      return dynamic(() => import('./components/linktable_outlined').then((mod) => mod.default));

    case 'components/percent_outlined':
      return dynamic(() => import('./components/percent_outlined').then((mod) => mod.default));

    case 'components/currency_CNY_outlined':
      return dynamic(() => import('./components/currency_CNY_outlined').then((mod) => mod.default));

    case 'components/formula_outlined':
      return dynamic(() => import('./components/formula_outlined').then((mod) => mod.default));

    case 'components/number_outlined':
      return dynamic(() => import('./components/number_outlined').then((mod) => mod.default));

    case 'components/attachment_outlined':
      return dynamic(() => import('./components/attachment_outlined').then((mod) => mod.default));

    case 'components/select_multiple_outlined':
      return dynamic(() => import('./components/select_multiple_outlined').then((mod) => mod.default));

    case 'components/select_single_outlined':
      return dynamic(() => import('./components/select_single_outlined').then((mod) => mod.default));

    case 'components/checkbox_outlined':
      return dynamic(() => import('./components/checkbox_outlined').then((mod) => mod.default));

    case 'components/text_outlined':
      return dynamic(() => import('./components/text_outlined').then((mod) => mod.default));

    case 'components/dashboard_filled':
      return dynamic(() => import('./components/dashboard_filled').then((mod) => mod.default));

    case 'components/class_outlined':
      return dynamic(() => import('./components/class_outlined').then((mod) => mod.default));

    case 'components/desktop_outlined':
      return dynamic(() => import('./components/desktop_outlined').then((mod) => mod.default));

    case 'components/mortarboard_outlined':
      return dynamic(() => import('./components/mortarboard_outlined').then((mod) => mod.default));

    case 'components/alarm_outlined':
      return dynamic(() => import('./components/alarm_outlined').then((mod) => mod.default));

    case 'components/star_outlined':
      return dynamic(() => import('./components/star_outlined').then((mod) => mod.default));

    case 'components/star_filled':
      return dynamic(() => import('./components/star_filled').then((mod) => mod.default));

    case 'components/qrcode_outlined':
      return dynamic(() => import('./components/qrcode_outlined').then((mod) => mod.default));

    case 'components/notification_outlined':
      return dynamic(() => import('./components/notification_outlined').then((mod) => mod.default));

    case 'components/gift_outlined':
      return dynamic(() => import('./components/gift_outlined').then((mod) => mod.default));

    case 'components/shield_check_outlined':
      return dynamic(() => import('./components/shield_check_outlined').then((mod) => mod.default));

    case 'components/more_outlined':
      return dynamic(() => import('./components/more_outlined').then((mod) => mod.default));

    case 'components/dashboard_outlined':
      return dynamic(() => import('./components/dashboard_outlined').then((mod) => mod.default));

    case 'components/import_outlined':
      return dynamic(() => import('./components/import_outlined').then((mod) => mod.default));

    case 'components/group_outlined':
      return dynamic(() => import('./components/group_outlined').then((mod) => mod.default));

    case 'components/sync_on_filled':
      return dynamic(() => import('./components/sync_on_filled').then((mod) => mod.default));

    case 'components/rank_up_filled':
      return dynamic(() => import('./components/rank_up_filled').then((mod) => mod.default));

    case 'components/sync_on_outlined':
      return dynamic(() => import('./components/sync_on_outlined').then((mod) => mod.default));

    case 'components/eye_close_filled':
      return dynamic(() => import('./components/eye_close_filled').then((mod) => mod.default));

    case 'components/eye_open_filled':
      return dynamic(() => import('./components/eye_open_filled').then((mod) => mod.default));

    case 'components/collapse_2_outlined':
      return dynamic(() => import('./components/collapse_2_outlined').then((mod) => mod.default));

    case 'components/collapse_2_open_outlined':
      return dynamic(() => import('./components/collapse_2_open_outlined').then((mod) => mod.default));

    case 'components/collapse_outlined':
      return dynamic(() => import('./components/collapse_outlined').then((mod) => mod.default));

    case 'components/collapse_open_outlined':
      return dynamic(() => import('./components/collapse_open_outlined').then((mod) => mod.default));

    case 'components/comment_filled':
      return dynamic(() => import('./components/comment_filled').then((mod) => mod.default));

    case 'components/collapse_open_filled':
      return dynamic(() => import('./components/collapse_open_filled').then((mod) => mod.default));

    case 'components/close_outlined':
      return dynamic(() => import('./components/close_outlined').then((mod) => mod.default));

    case 'components/autosave_outlined':
      return dynamic(() => import('./components/autosave_outlined').then((mod) => mod.default));

    case 'components/sync_off_outlined':
      return dynamic(() => import('./components/sync_off_outlined').then((mod) => mod.default));

    case 'components/test_outlined':
      return dynamic(() => import('./components/test_outlined').then((mod) => mod.default));

    case 'components/sync_off_filled':
      return dynamic(() => import('./components/sync_off_filled').then((mod) => mod.default));

    case 'components/pause_filled':
      return dynamic(() => import('./components/pause_filled').then((mod) => mod.default));

    case 'components/play_filled':
      return dynamic(() => import('./components/play_filled').then((mod) => mod.default));

    case 'components/play_outlined':
      return dynamic(() => import('./components/play_outlined').then((mod) => mod.default));

    case 'components/pause_outlined':
      return dynamic(() => import('./components/pause_outlined').then((mod) => mod.default));

    case 'components/goto_outlined':
      return dynamic(() => import('./components/goto_outlined').then((mod) => mod.default));

    case 'components/goto_filled':
      return dynamic(() => import('./components/goto_filled').then((mod) => mod.default));

    case 'components/logout_outlined':
      return dynamic(() => import('./components/logout_outlined').then((mod) => mod.default));

    case 'components/import_filled':
      return dynamic(() => import('./components/import_filled').then((mod) => mod.default));

    case 'components/logout_filled':
      return dynamic(() => import('./components/logout_filled').then((mod) => mod.default));

    case 'components/connect_outlined':
      return dynamic(() => import('./components/connect_outlined').then((mod) => mod.default));

    case 'components/connect_filled':
      return dynamic(() => import('./components/connect_filled').then((mod) => mod.default));

    case 'components/setting_filled':
      return dynamic(() => import('./components/setting_filled').then((mod) => mod.default));

    case 'components/test_filled':
      return dynamic(() => import('./components/test_filled').then((mod) => mod.default));

    case 'components/group_filled':
      return dynamic(() => import('./components/group_filled').then((mod) => mod.default));

    case 'components/freeze_filled':
      return dynamic(() => import('./components/freeze_filled').then((mod) => mod.default));

    case 'components/autosave_filled':
      return dynamic(() => import('./components/autosave_filled').then((mod) => mod.default));

    case 'components/adjustment_filled':
      return dynamic(() => import('./components/adjustment_filled').then((mod) => mod.default));

    case 'components/position_filled':
      return dynamic(() => import('./components/position_filled').then((mod) => mod.default));

    case 'components/close_filled':
      return dynamic(() => import('./components/close_filled').then((mod) => mod.default));

    case 'components/collapse_filled':
      return dynamic(() => import('./components/collapse_filled').then((mod) => mod.default));

    case 'components/collapse_2_open_filled':
      return dynamic(() => import('./components/collapse_2_open_filled').then((mod) => mod.default));

    case 'components/collapse_2_filled':
      return dynamic(() => import('./components/collapse_2_filled').then((mod) => mod.default));

    case 'components/edit_filled':
      return dynamic(() => import('./components/edit_filled').then((mod) => mod.default));

    case 'components/expand_filled':
      return dynamic(() => import('./components/expand_filled').then((mod) => mod.default));

    case 'components/narrow_filled':
      return dynamic(() => import('./components/narrow_filled').then((mod) => mod.default));

    case 'components/copy_filled':
      return dynamic(() => import('./components/copy_filled').then((mod) => mod.default));

    case 'components/duplicate_filled':
      return dynamic(() => import('./components/duplicate_filled').then((mod) => mod.default));

    case 'components/undo_filled':
      return dynamic(() => import('./components/undo_filled').then((mod) => mod.default));

    case 'components/redo_filled':
      return dynamic(() => import('./components/redo_filled').then((mod) => mod.default));

    case 'components/disabled_filled':
      return dynamic(() => import('./components/disabled_filled').then((mod) => mod.default));

    case 'components/add_filled':
      return dynamic(() => import('./components/add_filled').then((mod) => mod.default));

    case 'components/rank_down_filled':
      return dynamic(() => import('./components/rank_down_filled').then((mod) => mod.default));

    case 'components/rank_filled':
      return dynamic(() => import('./components/rank_filled').then((mod) => mod.default));

    case 'components/newtab_filled':
      return dynamic(() => import('./components/newtab_filled').then((mod) => mod.default));

    case 'components/drag_filled':
      return dynamic(() => import('./components/drag_filled').then((mod) => mod.default));

    case 'components/restore_outlined':
      return dynamic(() => import('./components/restore_outlined').then((mod) => mod.default));

    case 'components/reload_filled':
      return dynamic(() => import('./components/reload_filled').then((mod) => mod.default));

    case 'components/restore_filled':
      return dynamic(() => import('./components/restore_filled').then((mod) => mod.default));

    case 'components/look_up_filled':
      return dynamic(() => import('./components/look_up_filled').then((mod) => mod.default));

    case 'components/search_filled':
      return dynamic(() => import('./components/search_filled').then((mod) => mod.default));

    case 'components/check_filled':
      return dynamic(() => import('./components/check_filled').then((mod) => mod.default));

    case 'components/filter_filled':
      return dynamic(() => import('./components/filter_filled').then((mod) => mod.default));

    case 'components/freeze_outlined':
      return dynamic(() => import('./components/freeze_outlined').then((mod) => mod.default));

    case 'components/position_outlined':
      return dynamic(() => import('./components/position_outlined').then((mod) => mod.default));

    case 'components/comment_outlined':
      return dynamic(() => import('./components/comment_outlined').then((mod) => mod.default));

    case 'components/disabled_outlined':
      return dynamic(() => import('./components/disabled_outlined').then((mod) => mod.default));

    case 'components/setting_outlined':
      return dynamic(() => import('./components/setting_outlined').then((mod) => mod.default));

    case 'components/rank_outlined':
      return dynamic(() => import('./components/rank_outlined').then((mod) => mod.default));

    case 'components/filter_outlined':
      return dynamic(() => import('./components/filter_outlined').then((mod) => mod.default));

    case 'components/redo_outlined':
      return dynamic(() => import('./components/redo_outlined').then((mod) => mod.default));

    case 'components/undo_outlined':
      return dynamic(() => import('./components/undo_outlined').then((mod) => mod.default));

    case 'components/lookup_outlined':
      return dynamic(() => import('./components/lookup_outlined').then((mod) => mod.default));

    case 'components/eye_open_outlined':
      return dynamic(() => import('./components/eye_open_outlined').then((mod) => mod.default));

    case 'components/eye_close_outlined':
      return dynamic(() => import('./components/eye_close_outlined').then((mod) => mod.default));

    case 'components/collapse_3_open_outlined':
      return dynamic(() => import('./components/collapse_3_open_outlined').then((mod) => mod.default));

    case 'components/collapse_3_outlined':
      return dynamic(() => import('./components/collapse_3_outlined').then((mod) => mod.default));

    case 'components/rank_down_outlined':
      return dynamic(() => import('./components/rank_down_outlined').then((mod) => mod.default));

    case 'components/share_filled':
      return dynamic(() => import('./components/share_filled').then((mod) => mod.default));

    case 'components/rank_up_outlined':
      return dynamic(() => import('./components/rank_up_outlined').then((mod) => mod.default));

    case 'components/share_outlined':
      return dynamic(() => import('./components/share_outlined').then((mod) => mod.default));

    case 'components/paste_outlined':
      return dynamic(() => import('./components/paste_outlined').then((mod) => mod.default));

    case 'components/subtract_circle_filled':
      return dynamic(() => import('./components/subtract_circle_filled').then((mod) => mod.default));

    case 'components/subtract_circle_outlined':
      return dynamic(() => import('./components/subtract_circle_outlined').then((mod) => mod.default));

    case 'components/lock_outlined':
      return dynamic(() => import('./components/lock_outlined').then((mod) => mod.default));

    case 'components/drag_outlined':
      return dynamic(() => import('./components/drag_outlined').then((mod) => mod.default));

    case 'components/lock_filled':
      return dynamic(() => import('./components/lock_filled').then((mod) => mod.default));

    case 'components/newtab_outlined':
      return dynamic(() => import('./components/newtab_outlined').then((mod) => mod.default));

    case 'components/search_outlined':
      return dynamic(() => import('./components/search_outlined').then((mod) => mod.default));

    case 'components/reload_outlined':
      return dynamic(() => import('./components/reload_outlined').then((mod) => mod.default));

    case 'components/add_circle_filled':
      return dynamic(() => import('./components/add_circle_filled').then((mod) => mod.default));

    case 'components/check_outlined':
      return dynamic(() => import('./components/check_outlined').then((mod) => mod.default));

    case 'components/duplicate_outlined':
      return dynamic(() => import('./components/duplicate_outlined').then((mod) => mod.default));

    case 'components/edit_outlined':
      return dynamic(() => import('./components/edit_outlined').then((mod) => mod.default));

    case 'components/narrow_outlined':
      return dynamic(() => import('./components/narrow_outlined').then((mod) => mod.default));

    case 'components/expand_outlined':
      return dynamic(() => import('./components/expand_outlined').then((mod) => mod.default));

    case 'components/copy_outlined':
      return dynamic(() => import('./components/copy_outlined').then((mod) => mod.default));

    case 'components/add_outlined':
      return dynamic(() => import('./components/add_outlined').then((mod) => mod.default));

    case 'components/triangle_right_outlined':
      return dynamic(() => import('./components/triangle_right_outlined').then((mod) => mod.default));

    case 'components/triangle_up_outlined':
      return dynamic(() => import('./components/triangle_up_outlined').then((mod) => mod.default));

    case 'components/triangle_down_outlined':
      return dynamic(() => import('./components/triangle_down_outlined').then((mod) => mod.default));

    case 'components/triangle_left_outlined':
      return dynamic(() => import('./components/triangle_left_outlined').then((mod) => mod.default));

    case 'components/arrow_up_filled':
      return dynamic(() => import('./components/arrow_up_filled').then((mod) => mod.default));

    case 'components/arrow_right_filled':
      return dynamic(() => import('./components/arrow_right_filled').then((mod) => mod.default));

    case 'components/arrow_down_filled':
      return dynamic(() => import('./components/arrow_down_filled').then((mod) => mod.default));

    case 'components/arrow_left_filled':
      return dynamic(() => import('./components/arrow_left_filled').then((mod) => mod.default));

    case 'components/chevron_double_up_filled':
      return dynamic(() => import('./components/chevron_double_up_filled').then((mod) => mod.default));

    case 'components/chevron_double_down_filled':
      return dynamic(() => import('./components/chevron_double_down_filled').then((mod) => mod.default));

    case 'components/chevron_double_right_filled':
      return dynamic(() => import('./components/chevron_double_right_filled').then((mod) => mod.default));

    case 'components/chevron_double_left_filled':
      return dynamic(() => import('./components/chevron_double_left_filled').then((mod) => mod.default));

    case 'components/chevron_up_filled':
      return dynamic(() => import('./components/chevron_up_filled').then((mod) => mod.default));

    case 'components/chevron_right_filled':
      return dynamic(() => import('./components/chevron_right_filled').then((mod) => mod.default));

    case 'components/chevron_down_filled':
      return dynamic(() => import('./components/chevron_down_filled').then((mod) => mod.default));

    case 'components/chevron_left_filled':
      return dynamic(() => import('./components/chevron_left_filled').then((mod) => mod.default));

    case 'components/chevron_double_up_outlined':
      return dynamic(() => import('./components/chevron_double_up_outlined').then((mod) => mod.default));

    case 'components/chevron_double_down_outlined':
      return dynamic(() => import('./components/chevron_double_down_outlined').then((mod) => mod.default));

    case 'components/chevron_double_right_outlined':
      return dynamic(() => import('./components/chevron_double_right_outlined').then((mod) => mod.default));

    case 'components/chevron_double_left_outlined':
      return dynamic(() => import('./components/chevron_double_left_outlined').then((mod) => mod.default));

    case 'components/triangle_down_filled':
      return dynamic(() => import('./components/triangle_down_filled').then((mod) => mod.default));

    case 'components/triangle_right_filled':
      return dynamic(() => import('./components/triangle_right_filled').then((mod) => mod.default));

    case 'components/triangle_up_filled':
      return dynamic(() => import('./components/triangle_up_filled').then((mod) => mod.default));

    case 'components/triangle_left_filled':
      return dynamic(() => import('./components/triangle_left_filled').then((mod) => mod.default));

    case 'components/chevron_up_outlined':
      return dynamic(() => import('./components/chevron_up_outlined').then((mod) => mod.default));

    case 'components/chevron_right_outlined':
      return dynamic(() => import('./components/chevron_right_outlined').then((mod) => mod.default));

    case 'components/chevron_down_outlined':
      return dynamic(() => import('./components/chevron_down_outlined').then((mod) => mod.default));

    case 'components/arrow_left_outlined':
      return dynamic(() => import('./components/arrow_left_outlined').then((mod) => mod.default));

    case 'components/arrow_right_outlined':
      return dynamic(() => import('./components/arrow_right_outlined').then((mod) => mod.default));

    case 'components/arrow_up_outlined':
      return dynamic(() => import('./components/arrow_up_outlined').then((mod) => mod.default));

    case 'components/arrow_down_outlined':
      return dynamic(() => import('./components/arrow_down_outlined').then((mod) => mod.default));

    default:
      throw new Error(`Unknown icon type: ${iconType}`);
  }
}
