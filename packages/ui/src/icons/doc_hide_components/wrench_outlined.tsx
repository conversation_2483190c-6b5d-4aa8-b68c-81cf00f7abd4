
/* eslint-disable max-len */
import React from 'react';
import { makeIcon, IIconProps } from '../utils/icon';

const WrenchOutlined: React.FC<IIconProps> = makeIcon({
    Path: ({ colors }) => <>
    <path d="M5.00715 1.39094C6.50958 1.07179 8.14171 1.4957 9.31086 2.66438C10.5067 3.86018 10.9217 5.53957 10.5609 7.06965C10.5074 7.29608 10.5688 7.4572 10.6419 7.53059L14.2757 11.1654C14.9396 11.8292 14.9396 12.9048 14.2757 13.5687L13.5687 14.2757C12.9048 14.9396 11.8292 14.9396 11.1653 14.2757L7.53058 10.6419C7.4572 10.5688 7.29608 10.5074 7.06965 10.5609C5.53957 10.9217 3.86018 10.5067 2.66437 9.31086C1.49569 8.14171 1.07179 6.50958 1.39094 5.00715V5.00617C1.52285 4.38754 1.99075 4.00422 2.51691 3.90852C3.01967 3.81721 3.56043 3.98457 3.94855 4.37238L5.06867 5.4925C5.18583 5.60966 5.37534 5.60966 5.4925 5.4925C5.60966 5.37534 5.60966 5.18583 5.4925 5.06867L4.37238 3.94855C3.98457 3.56043 3.81721 3.01967 3.90851 2.51691C4.00421 1.99076 4.38754 1.52285 5.00617 1.39094H5.00715ZM8.3216 3.65461C7.50148 2.8348 6.35644 2.53534 5.29816 2.76008C5.29341 2.76109 5.2893 2.76212 5.28644 2.76301C5.28619 2.76411 5.28574 2.76555 5.28547 2.76691C5.27882 2.80354 5.28922 2.88462 5.36261 2.95832L6.48273 4.07844C7.14662 4.74233 7.14662 5.81884 6.48273 6.48273C5.81884 7.14663 4.74233 7.14663 4.07844 6.48273L2.95832 5.36262C2.88462 5.28922 2.80353 5.27882 2.76691 5.28547C2.76555 5.28574 2.76411 5.28619 2.76301 5.28645C2.76212 5.2893 2.76109 5.29341 2.76008 5.29816C2.53533 6.35644 2.8348 7.50148 3.65461 8.3216C4.49295 9.15974 5.67034 9.45282 6.74836 9.19855C7.31435 9.06499 8.01798 9.14913 8.52082 9.65168L12.1546 13.2864C12.2718 13.4036 12.4623 13.4036 12.5794 13.2864L13.2864 12.5794C13.4036 12.4623 13.4036 12.2718 13.2864 12.1546L9.65168 8.52082C9.14913 8.01798 9.06499 7.31436 9.19855 6.74836C9.45282 5.67035 9.15974 4.49295 8.3216 3.65461Z" fill={ colors[0] }/>

  </>,
  name: 'wrench_outlined',
  defaultColors: ['#D9D9D9'],
  colorful: false,
  allPathData: ['M5.00715 1.39094C6.50958 1.07179 8.14171 1.4957 9.31086 2.66438C10.5067 3.86018 10.9217 5.53957 10.5609 7.06965C10.5074 7.29608 10.5688 7.4572 10.6419 7.53059L14.2757 11.1654C14.9396 11.8292 14.9396 12.9048 14.2757 13.5687L13.5687 14.2757C12.9048 14.9396 11.8292 14.9396 11.1653 14.2757L7.53058 10.6419C7.4572 10.5688 7.29608 10.5074 7.06965 10.5609C5.53957 10.9217 3.86018 10.5067 2.66437 9.31086C1.49569 8.14171 1.07179 6.50958 1.39094 5.00715V5.00617C1.52285 4.38754 1.99075 4.00422 2.51691 3.90852C3.01967 3.81721 3.56043 3.98457 3.94855 4.37238L5.06867 5.4925C5.18583 5.60966 5.37534 5.60966 5.4925 5.4925C5.60966 5.37534 5.60966 5.18583 5.4925 5.06867L4.37238 3.94855C3.98457 3.56043 3.81721 3.01967 3.90851 2.51691C4.00421 1.99076 4.38754 1.52285 5.00617 1.39094H5.00715ZM8.3216 3.65461C7.50148 2.8348 6.35644 2.53534 5.29816 2.76008C5.29341 2.76109 5.2893 2.76212 5.28644 2.76301C5.28619 2.76411 5.28574 2.76555 5.28547 2.76691C5.27882 2.80354 5.28922 2.88462 5.36261 2.95832L6.48273 4.07844C7.14662 4.74233 7.14662 5.81884 6.48273 6.48273C5.81884 7.14663 4.74233 7.14663 4.07844 6.48273L2.95832 5.36262C2.88462 5.28922 2.80353 5.27882 2.76691 5.28547C2.76555 5.28574 2.76411 5.28619 2.76301 5.28645C2.76212 5.2893 2.76109 5.29341 2.76008 5.29816C2.53533 6.35644 2.8348 7.50148 3.65461 8.3216C4.49295 9.15974 5.67034 9.45282 6.74836 9.19855C7.31435 9.06499 8.01798 9.14913 8.52082 9.65168L12.1546 13.2864C12.2718 13.4036 12.4623 13.4036 12.5794 13.2864L13.2864 12.5794C13.4036 12.4623 13.4036 12.2718 13.2864 12.1546L9.65168 8.52082C9.14913 8.01798 9.06499 7.31436 9.19855 6.74836C9.45282 5.67035 9.15974 4.49295 8.3216 3.65461Z'],
  width: '16',
  height: '16',
  viewBox: '0 0 16 16',
});
export default WrenchOutlined;
