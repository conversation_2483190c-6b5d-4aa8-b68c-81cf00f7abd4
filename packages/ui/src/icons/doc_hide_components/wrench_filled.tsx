
/* eslint-disable max-len */
import React from 'react';
import { makeIcon, IIconProps } from '../utils/icon';

const WrenchFilled: React.FC<IIconProps> = makeIcon({
    Path: ({ colors }) => <>
    <path d="M5.00755 1.39134C6.50996 1.07223 8.14214 1.49611 9.31126 2.66478C10.507 3.86058 10.9221 5.54003 10.5613 7.07005C10.5079 7.29645 10.5693 7.45762 10.6423 7.53099L14.2761 11.1658C14.9398 11.8297 14.9399 12.9052 14.2761 13.5691L13.5691 14.2761C12.9052 14.9399 11.8297 14.9398 11.1658 14.2761L7.53099 10.6423C7.45762 10.5693 7.29645 10.5079 7.07005 10.5613C5.54003 10.9221 3.86058 10.507 2.66478 9.31126C1.49611 8.14214 1.07223 6.50996 1.39134 5.00755C1.52325 4.3889 1.99114 4.00461 2.51732 3.90892C3.02005 3.81766 3.56086 3.98499 3.94896 4.37278L5.06907 5.4929C5.18624 5.60991 5.37579 5.61001 5.4929 5.4929C5.61001 5.3758 5.6099 5.18624 5.4929 5.06907L4.37278 3.94896C3.98499 3.56086 3.81766 3.02005 3.90892 2.51732C4.00461 1.99115 4.3889 1.52325 5.00755 1.39134Z" fill={ colors[0] }/>

  </>,
  name: 'wrench_filled',
  defaultColors: ['#D9D9D9'],
  colorful: false,
  allPathData: ['M5.00755 1.39134C6.50996 1.07223 8.14214 1.49611 9.31126 2.66478C10.507 3.86058 10.9221 5.54003 10.5613 7.07005C10.5079 7.29645 10.5693 7.45762 10.6423 7.53099L14.2761 11.1658C14.9398 11.8297 14.9399 12.9052 14.2761 13.5691L13.5691 14.2761C12.9052 14.9399 11.8297 14.9398 11.1658 14.2761L7.53099 10.6423C7.45762 10.5693 7.29645 10.5079 7.07005 10.5613C5.54003 10.9221 3.86058 10.507 2.66478 9.31126C1.49611 8.14214 1.07223 6.50996 1.39134 5.00755C1.52325 4.3889 1.99114 4.00461 2.51732 3.90892C3.02005 3.81766 3.56086 3.98499 3.94896 4.37278L5.06907 5.4929C5.18624 5.60991 5.37579 5.61001 5.4929 5.4929C5.61001 5.3758 5.6099 5.18624 5.4929 5.06907L4.37278 3.94896C3.98499 3.56086 3.81766 3.02005 3.90892 2.51732C4.00461 1.99115 4.3889 1.52325 5.00755 1.39134Z'],
  width: '16',
  height: '16',
  viewBox: '0 0 16 16',
});
export default WrenchFilled;
